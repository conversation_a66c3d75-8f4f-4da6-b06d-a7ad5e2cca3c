"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: () => (/* binding */ PopChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* __next_internal_client_entry_do_not_use__ PopChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */ class PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const parent = element.offsetParent;\n            const parentWidth = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(parent) ? parent.offsetWidth || 0 : 0;\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n            size.right = parentWidth - size.width - size.left;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */ componentDidUpdate() {}\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild(param) {\n    let { children, isPresent, anchorX } = param;\n    _s();\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        right: 0\n    });\n    const { nonce } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect)({\n        \"PopChild.useInsertionEffect\": ()=>{\n            const { width, height, top, left, right } = size.current;\n            if (isPresent || !ref.current || !width || !height) return;\n            const x = anchorX === \"left\" ? \"left: \".concat(left) : \"right: \".concat(right);\n            ref.current.dataset.motionPopId = id;\n            const style = document.createElement(\"style\");\n            if (nonce) style.nonce = nonce;\n            document.head.appendChild(style);\n            if (style.sheet) {\n                style.sheet.insertRule('\\n          [data-motion-pop-id=\"'.concat(id, '\"] {\\n            position: absolute !important;\\n            width: ').concat(width, \"px !important;\\n            height: \").concat(height, \"px !important;\\n            \").concat(x, \"px !important;\\n            top: \").concat(top, \"px !important;\\n          }\\n        \"));\n            }\n            return ({\n                \"PopChild.useInsertionEffect\": ()=>{\n                    if (document.head.contains(style)) {\n                        document.head.removeChild(style);\n                    }\n                }\n            })[\"PopChild.useInsertionEffect\"];\n        }\n    }[\"PopChild.useInsertionEffect\"], [\n        isPresent\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PopChildMeasure, {\n        isPresent: isPresent,\n        childRef: ref,\n        sizeRef: size,\n        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            ref\n        })\n    });\n}\n_s(PopChild, \"V7z789Ed2n0+HnmYCJ8kEL0I644=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_1__.useId,\n        react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect\n    ];\n});\n_c = PopChild;\n\nvar _c;\n$RefreshReg$(_c, \"PopChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: () => (/* binding */ PresenceChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n/* __next_internal_client_entry_do_not_use__ PresenceChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\nconst PresenceChild = (param)=>{\n    let { children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, anchorX } = param;\n    _s();\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    let isReusedContext = true;\n    let context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo[context]\": ()=>{\n            isReusedContext = false;\n            return {\n                id,\n                initial,\n                isPresent,\n                custom,\n                onExitComplete: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, true);\n                        for (const isComplete of presenceChildren.values()){\n                            if (!isComplete) return; // can stop searching when any is incomplete\n                        }\n                        onExitComplete && onExitComplete();\n                    }\n                })[\"PresenceChild.useMemo[context]\"],\n                register: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, false);\n                        return ({\n                            \"PresenceChild.useMemo[context]\": ()=>presenceChildren.delete(childId)\n                        })[\"PresenceChild.useMemo[context]\"];\n                    }\n                })[\"PresenceChild.useMemo[context]\"]\n            };\n        }\n    }[\"PresenceChild.useMemo[context]\"], [\n        isPresent,\n        presenceChildren,\n        onExitComplete\n    ]);\n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */ if (presenceAffectsLayout && isReusedContext) {\n        context = {\n            ...context\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo\": ()=>{\n            presenceChildren.forEach({\n                \"PresenceChild.useMemo\": (_, key)=>presenceChildren.set(key, false)\n            }[\"PresenceChild.useMemo\"]);\n        }\n    }[\"PresenceChild.useMemo\"], [\n        isPresent\n    ]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */ react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PresenceChild.useEffect\": ()=>{\n            !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n        }\n    }[\"PresenceChild.useEffect\"], [\n        isPresent\n    ]);\n    if (mode === \"popLayout\") {\n        children = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__.PopChild, {\n            isPresent: isPresent,\n            anchorX: anchorX,\n            children: children\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__.PresenceContext.Provider, {\n        value: context,\n        children: children\n    });\n};\n_s(PresenceChild, \"LuJRAK72iQdFH7nv0cJ6ZjdNWv8=\", false, function() {\n    return [\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant,\n        react__WEBPACK_IMPORTED_MODULE_1__.useId\n    ];\n});\n_c = PresenceChild;\nfunction newChildrenMap() {\n    return new Map();\n}\n\nvar _c;\n$RefreshReg$(_c, \"PresenceChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: () => (/* binding */ AnimatePresence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-presence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ AnimatePresence auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */ const AnimatePresence = (param)=>{\n    let { children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false, anchorX = \"left\" } = param;\n    _s();\n    const [isParentPresent, safeToRemove] = (0,_use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence)(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */ const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnimatePresence.useMemo[presentChildren]\": ()=>(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(children)\n    }[\"AnimatePresence.useMemo[presentChildren]\"], [\n        children\n    ]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */ const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */ const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */ const pendingPresentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */ const exitComplete = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"AnimatePresence.useConstant[exitComplete]\": ()=>new Map()\n    }[\"AnimatePresence.useConstant[exitComplete]\"]);\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */ const [diffedChildren, setDiffedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    const [renderedChildren, setRenderedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)({\n        \"AnimatePresence.useIsomorphicLayoutEffect\": ()=>{\n            isInitialRender.current = false;\n            pendingPresentChildren.current = presentChildren;\n            /**\n         * Update complete status of exiting children.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n                const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(renderedChildren[i]);\n                if (!presentKeys.includes(key)) {\n                    if (exitComplete.get(key) !== true) {\n                        exitComplete.set(key, false);\n                    }\n                } else {\n                    exitComplete.delete(key);\n                }\n            }\n        }\n    }[\"AnimatePresence.useIsomorphicLayoutEffect\"], [\n        renderedChildren,\n        presentKeys.length,\n        presentKeys.join(\"-\")\n    ]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [\n            ...presentChildren\n        ];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n            const child = renderedChildren[i];\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */ if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */ return null;\n    }\n    if ( true && mode === \"wait\" && renderedChildren.length > 1) {\n        console.warn('You\\'re attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.');\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */ const { forceRender } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: renderedChildren.map((child)=>{\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            const isPresent = propagate && !isParentPresent ? false : presentChildren === renderedChildren || presentKeys.includes(key);\n            const onExit = ()=>{\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                } else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete)=>{\n                    if (!isExitComplete) isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender === null || forceRender === void 0 ? void 0 : forceRender();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && (safeToRemove === null || safeToRemove === void 0 ? void 0 : safeToRemove());\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, {\n                isPresent: isPresent,\n                initial: !isInitialRender.current || initial ? undefined : false,\n                custom: custom,\n                presenceAffectsLayout: presenceAffectsLayout,\n                mode: mode,\n                onExitComplete: isPresent ? undefined : onExit,\n                anchorX: anchorX,\n                children: child\n            }, key);\n        })\n    });\n};\n_s(AnimatePresence, \"hskVsE2zKTQdrb/joPYe18qtIRg=\", false, function() {\n    return [\n        _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence,\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant,\n        _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = AnimatePresence;\n\nvar _c;\n$RefreshReg$(_c, \"AnimatePresence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDWTtBQUNZO0FBQ2Y7QUFDdUI7QUFDOUI7QUFDSDtBQUNPO0FBRXhEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQWdDQyxHQUNELE1BQU1hLGtCQUFrQjtRQUFDLEVBQUVDLFFBQVEsRUFBRUMsTUFBTSxFQUFFQyxVQUFVLElBQUksRUFBRUMsY0FBYyxFQUFFQyx3QkFBd0IsSUFBSSxFQUFFQyxPQUFPLE1BQU0sRUFBRUMsWUFBWSxLQUFLLEVBQUVDLFVBQVUsTUFBTSxFQUFHOztJQUM1SixNQUFNLENBQUNDLGlCQUFpQkMsYUFBYSxHQUFHYiw4REFBV0EsQ0FBQ1U7SUFDcEQ7OztLQUdDLEdBQ0QsTUFBTUksa0JBQWtCdEIsOENBQU9BO29EQUFDLElBQU1TLHdEQUFZQSxDQUFDRzttREFBVztRQUFDQTtLQUFTO0lBQ3hFOzs7S0FHQyxHQUNELE1BQU1XLGNBQWNMLGFBQWEsQ0FBQ0Usa0JBQWtCLEVBQUUsR0FBR0UsZ0JBQWdCRSxHQUFHLENBQUNkLG1EQUFXQTtJQUN4Rjs7S0FFQyxHQUNELE1BQU1lLGtCQUFrQnhCLDZDQUFNQSxDQUFDO0lBQy9COzs7O0tBSUMsR0FDRCxNQUFNeUIseUJBQXlCekIsNkNBQU1BLENBQUNxQjtJQUN0Qzs7S0FFQyxHQUNELE1BQU1LLGVBQWV0QixvRUFBV0E7cURBQUMsSUFBTSxJQUFJdUI7O0lBQzNDOzs7S0FHQyxHQUNELE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBRzVCLCtDQUFRQSxDQUFDb0I7SUFDckQsTUFBTSxDQUFDUyxrQkFBa0JDLG9CQUFvQixHQUFHOUIsK0NBQVFBLENBQUNvQjtJQUN6RGhCLDJGQUF5QkE7cURBQUM7WUFDdEJtQixnQkFBZ0JRLE9BQU8sR0FBRztZQUMxQlAsdUJBQXVCTyxPQUFPLEdBQUdYO1lBQ2pDOztTQUVDLEdBQ0QsSUFBSyxJQUFJWSxJQUFJLEdBQUdBLElBQUlILGlCQUFpQkksTUFBTSxFQUFFRCxJQUFLO2dCQUM5QyxNQUFNRSxNQUFNMUIsdURBQVdBLENBQUNxQixnQkFBZ0IsQ0FBQ0csRUFBRTtnQkFDM0MsSUFBSSxDQUFDWCxZQUFZYyxRQUFRLENBQUNELE1BQU07b0JBQzVCLElBQUlULGFBQWFXLEdBQUcsQ0FBQ0YsU0FBUyxNQUFNO3dCQUNoQ1QsYUFBYVksR0FBRyxDQUFDSCxLQUFLO29CQUMxQjtnQkFDSixPQUNLO29CQUNEVCxhQUFhYSxNQUFNLENBQUNKO2dCQUN4QjtZQUNKO1FBQ0o7b0RBQUc7UUFBQ0w7UUFBa0JSLFlBQVlZLE1BQU07UUFBRVosWUFBWWtCLElBQUksQ0FBQztLQUFLO0lBQ2hFLE1BQU1DLGtCQUFrQixFQUFFO0lBQzFCLElBQUlwQixvQkFBb0JPLGdCQUFnQjtRQUNwQyxJQUFJYyxlQUFlO2VBQUlyQjtTQUFnQjtRQUN2Qzs7O1NBR0MsR0FDRCxJQUFLLElBQUlZLElBQUksR0FBR0EsSUFBSUgsaUJBQWlCSSxNQUFNLEVBQUVELElBQUs7WUFDOUMsTUFBTVUsUUFBUWIsZ0JBQWdCLENBQUNHLEVBQUU7WUFDakMsTUFBTUUsTUFBTTFCLHVEQUFXQSxDQUFDa0M7WUFDeEIsSUFBSSxDQUFDckIsWUFBWWMsUUFBUSxDQUFDRCxNQUFNO2dCQUM1Qk8sYUFBYUUsTUFBTSxDQUFDWCxHQUFHLEdBQUdVO2dCQUMxQkYsZ0JBQWdCSSxJQUFJLENBQUNGO1lBQ3pCO1FBQ0o7UUFDQTs7O1NBR0MsR0FDRCxJQUFJM0IsU0FBUyxVQUFVeUIsZ0JBQWdCUCxNQUFNLEVBQUU7WUFDM0NRLGVBQWVEO1FBQ25CO1FBQ0FWLG9CQUFvQnZCLHdEQUFZQSxDQUFDa0M7UUFDakNiLGtCQUFrQlI7UUFDbEI7OztTQUdDLEdBQ0QsT0FBTztJQUNYO0lBQ0EsSUFBSXlCLEtBQXFDLElBQ3JDOUIsU0FBUyxVQUNUYyxpQkFBaUJJLE1BQU0sR0FBRyxHQUFHO1FBQzdCYSxRQUFRQyxJQUFJLENBQUU7SUFDbEI7SUFDQTs7OztLQUlDLEdBQ0QsTUFBTSxFQUFFQyxXQUFXLEVBQUUsR0FBRy9DLGlEQUFVQSxDQUFDQywrRUFBa0JBO0lBQ3JELE9BQVFOLHNEQUFHQSxDQUFDQyx1REFBUUEsRUFBRTtRQUFFYSxVQUFVbUIsaUJBQWlCUCxHQUFHLENBQUMsQ0FBQ29CO1lBQ2hELE1BQU1SLE1BQU0xQix1REFBV0EsQ0FBQ2tDO1lBQ3hCLE1BQU1PLFlBQVlqQyxhQUFhLENBQUNFLGtCQUMxQixRQUNBRSxvQkFBb0JTLG9CQUNsQlIsWUFBWWMsUUFBUSxDQUFDRDtZQUM3QixNQUFNZ0IsU0FBUztnQkFDWCxJQUFJekIsYUFBYTBCLEdBQUcsQ0FBQ2pCLE1BQU07b0JBQ3ZCVCxhQUFhWSxHQUFHLENBQUNILEtBQUs7Z0JBQzFCLE9BQ0s7b0JBQ0Q7Z0JBQ0o7Z0JBQ0EsSUFBSWtCLHNCQUFzQjtnQkFDMUIzQixhQUFhNEIsT0FBTyxDQUFDLENBQUNDO29CQUNsQixJQUFJLENBQUNBLGdCQUNERixzQkFBc0I7Z0JBQzlCO2dCQUNBLElBQUlBLHFCQUFxQjtvQkFDckJKLHdCQUFBQSxrQ0FBQUE7b0JBQ0FsQixvQkFBb0JOLHVCQUF1Qk8sT0FBTztvQkFDbERmLGNBQWFHLHlCQUFBQSxtQ0FBQUE7b0JBQ2JOLGtCQUFrQkE7Z0JBQ3RCO1lBQ0o7WUFDQSxPQUFRakIsc0RBQUdBLENBQUNTLDZEQUFhQSxFQUFFO2dCQUFFNEMsV0FBV0E7Z0JBQVdyQyxTQUFTLENBQUNXLGdCQUFnQlEsT0FBTyxJQUFJbkIsVUFDOUUyQyxZQUNBO2dCQUFPNUMsUUFBUUE7Z0JBQVFHLHVCQUF1QkE7Z0JBQXVCQyxNQUFNQTtnQkFBTUYsZ0JBQWdCb0MsWUFBWU0sWUFBWUw7Z0JBQVFqQyxTQUFTQTtnQkFBU1AsVUFBVWdDO1lBQU0sR0FBR1I7UUFDcEw7SUFBRztBQUNYO0dBeEhNekI7O1FBQ3NDSCwwREFBV0E7UUF3QjlCSCxnRUFBV0E7UUFPaENDLHVGQUF5QkE7OztLQWhDdkJLO0FBMEhxQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkYXZpZFxcRGVza3RvcFxcUHJvamVrdHlcXGJha2FzYW5hX3Byb2RcXGJha2FzYW5hX3Byb2RcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGNvbXBvbmVudHNcXEFuaW1hdGVQcmVzZW5jZVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsganN4LCBGcmFnbWVudCB9IGZyb20gJ3JlYWN0L2pzeC1ydW50aW1lJztcbmltcG9ydCB7IHVzZU1lbW8sIHVzZVJlZiwgdXNlU3RhdGUsIHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBMYXlvdXRHcm91cENvbnRleHQgfSBmcm9tICcuLi8uLi9jb250ZXh0L0xheW91dEdyb3VwQ29udGV4dC5tanMnO1xuaW1wb3J0IHsgdXNlQ29uc3RhbnQgfSBmcm9tICcuLi8uLi91dGlscy91c2UtY29uc3RhbnQubWpzJztcbmltcG9ydCB7IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgfSBmcm9tICcuLi8uLi91dGlscy91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzJztcbmltcG9ydCB7IFByZXNlbmNlQ2hpbGQgfSBmcm9tICcuL1ByZXNlbmNlQ2hpbGQubWpzJztcbmltcG9ydCB7IHVzZVByZXNlbmNlIH0gZnJvbSAnLi91c2UtcHJlc2VuY2UubWpzJztcbmltcG9ydCB7IG9ubHlFbGVtZW50cywgZ2V0Q2hpbGRLZXkgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbi8qKlxuICogYEFuaW1hdGVQcmVzZW5jZWAgZW5hYmxlcyB0aGUgYW5pbWF0aW9uIG9mIGNvbXBvbmVudHMgdGhhdCBoYXZlIGJlZW4gcmVtb3ZlZCBmcm9tIHRoZSB0cmVlLlxuICpcbiAqIFdoZW4gYWRkaW5nL3JlbW92aW5nIG1vcmUgdGhhbiBhIHNpbmdsZSBjaGlsZCwgZXZlcnkgY2hpbGQgKiptdXN0KiogYmUgZ2l2ZW4gYSB1bmlxdWUgYGtleWAgcHJvcC5cbiAqXG4gKiBBbnkgYG1vdGlvbmAgY29tcG9uZW50cyB0aGF0IGhhdmUgYW4gYGV4aXRgIHByb3BlcnR5IGRlZmluZWQgd2lsbCBhbmltYXRlIG91dCB3aGVuIHJlbW92ZWQgZnJvbVxuICogdGhlIHRyZWUuXG4gKlxuICogYGBganN4XG4gKiBpbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG4gKlxuICogZXhwb3J0IGNvbnN0IEl0ZW1zID0gKHsgaXRlbXMgfSkgPT4gKFxuICogICA8QW5pbWF0ZVByZXNlbmNlPlxuICogICAgIHtpdGVtcy5tYXAoaXRlbSA9PiAoXG4gKiAgICAgICA8bW90aW9uLmRpdlxuICogICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gKiAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICogICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAqICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gKiAgICAgICAvPlxuICogICAgICkpfVxuICogICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAqIClcbiAqIGBgYFxuICpcbiAqIFlvdSBjYW4gc2VxdWVuY2UgZXhpdCBhbmltYXRpb25zIHRocm91Z2hvdXQgYSB0cmVlIHVzaW5nIHZhcmlhbnRzLlxuICpcbiAqIElmIGEgY2hpbGQgY29udGFpbnMgbXVsdGlwbGUgYG1vdGlvbmAgY29tcG9uZW50cyB3aXRoIGBleGl0YCBwcm9wcywgaXQgd2lsbCBvbmx5IHVubW91bnQgdGhlIGNoaWxkXG4gKiBvbmNlIGFsbCBgbW90aW9uYCBjb21wb25lbnRzIGhhdmUgZmluaXNoZWQgYW5pbWF0aW5nIG91dC4gTGlrZXdpc2UsIGFueSBjb21wb25lbnRzIHVzaW5nXG4gKiBgdXNlUHJlc2VuY2VgIGFsbCBuZWVkIHRvIGNhbGwgYHNhZmVUb1JlbW92ZWAuXG4gKlxuICogQHB1YmxpY1xuICovXG5jb25zdCBBbmltYXRlUHJlc2VuY2UgPSAoeyBjaGlsZHJlbiwgY3VzdG9tLCBpbml0aWFsID0gdHJ1ZSwgb25FeGl0Q29tcGxldGUsIHByZXNlbmNlQWZmZWN0c0xheW91dCA9IHRydWUsIG1vZGUgPSBcInN5bmNcIiwgcHJvcGFnYXRlID0gZmFsc2UsIGFuY2hvclggPSBcImxlZnRcIiwgfSkgPT4ge1xuICAgIGNvbnN0IFtpc1BhcmVudFByZXNlbnQsIHNhZmVUb1JlbW92ZV0gPSB1c2VQcmVzZW5jZShwcm9wYWdhdGUpO1xuICAgIC8qKlxuICAgICAqIEZpbHRlciBhbnkgY2hpbGRyZW4gdGhhdCBhcmVuJ3QgUmVhY3RFbGVtZW50cy4gV2UgY2FuIG9ubHkgdHJhY2sgY29tcG9uZW50c1xuICAgICAqIGJldHdlZW4gcmVuZGVycyB3aXRoIGEgcHJvcHMua2V5LlxuICAgICAqL1xuICAgIGNvbnN0IHByZXNlbnRDaGlsZHJlbiA9IHVzZU1lbW8oKCkgPT4gb25seUVsZW1lbnRzKGNoaWxkcmVuKSwgW2NoaWxkcmVuXSk7XG4gICAgLyoqXG4gICAgICogVHJhY2sgdGhlIGtleXMgb2YgdGhlIGN1cnJlbnRseSByZW5kZXJlZCBjaGlsZHJlbi4gVGhpcyBpcyB1c2VkIHRvXG4gICAgICogZGV0ZXJtaW5lIHdoaWNoIGNoaWxkcmVuIGFyZSBleGl0aW5nLlxuICAgICAqL1xuICAgIGNvbnN0IHByZXNlbnRLZXlzID0gcHJvcGFnYXRlICYmICFpc1BhcmVudFByZXNlbnQgPyBbXSA6IHByZXNlbnRDaGlsZHJlbi5tYXAoZ2V0Q2hpbGRLZXkpO1xuICAgIC8qKlxuICAgICAqIElmIGBpbml0aWFsPXtmYWxzZX1gIHdlIG9ubHkgd2FudCB0byBwYXNzIHRoaXMgdG8gY29tcG9uZW50cyBpbiB0aGUgZmlyc3QgcmVuZGVyLlxuICAgICAqL1xuICAgIGNvbnN0IGlzSW5pdGlhbFJlbmRlciA9IHVzZVJlZih0cnVlKTtcbiAgICAvKipcbiAgICAgKiBBIHJlZiBjb250YWluaW5nIHRoZSBjdXJyZW50bHkgcHJlc2VudCBjaGlsZHJlbi4gV2hlbiBhbGwgZXhpdCBhbmltYXRpb25zXG4gICAgICogYXJlIGNvbXBsZXRlLCB3ZSB1c2UgdGhpcyB0byByZS1yZW5kZXIgdGhlIGNvbXBvbmVudCB3aXRoIHRoZSBsYXRlc3QgY2hpbGRyZW5cbiAgICAgKiAqY29tbWl0dGVkKiByYXRoZXIgdGhhbiB0aGUgbGF0ZXN0IGNoaWxkcmVuICpyZW5kZXJlZCouXG4gICAgICovXG4gICAgY29uc3QgcGVuZGluZ1ByZXNlbnRDaGlsZHJlbiA9IHVzZVJlZihwcmVzZW50Q2hpbGRyZW4pO1xuICAgIC8qKlxuICAgICAqIFRyYWNrIHdoaWNoIGV4aXRpbmcgY2hpbGRyZW4gaGF2ZSBmaW5pc2hlZCBhbmltYXRpbmcgb3V0LlxuICAgICAqL1xuICAgIGNvbnN0IGV4aXRDb21wbGV0ZSA9IHVzZUNvbnN0YW50KCgpID0+IG5ldyBNYXAoKSk7XG4gICAgLyoqXG4gICAgICogU2F2ZSBjaGlsZHJlbiB0byByZW5kZXIgYXMgUmVhY3Qgc3RhdGUuIFRvIGVuc3VyZSB0aGlzIGNvbXBvbmVudCBpcyBjb25jdXJyZW50LXNhZmUsXG4gICAgICogd2UgY2hlY2sgZm9yIGV4aXRpbmcgY2hpbGRyZW4gdmlhIGFuIGVmZmVjdC5cbiAgICAgKi9cbiAgICBjb25zdCBbZGlmZmVkQ2hpbGRyZW4sIHNldERpZmZlZENoaWxkcmVuXSA9IHVzZVN0YXRlKHByZXNlbnRDaGlsZHJlbik7XG4gICAgY29uc3QgW3JlbmRlcmVkQ2hpbGRyZW4sIHNldFJlbmRlcmVkQ2hpbGRyZW5dID0gdXNlU3RhdGUocHJlc2VudENoaWxkcmVuKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgICAgaXNJbml0aWFsUmVuZGVyLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgcGVuZGluZ1ByZXNlbnRDaGlsZHJlbi5jdXJyZW50ID0gcHJlc2VudENoaWxkcmVuO1xuICAgICAgICAvKipcbiAgICAgICAgICogVXBkYXRlIGNvbXBsZXRlIHN0YXR1cyBvZiBleGl0aW5nIGNoaWxkcmVuLlxuICAgICAgICAgKi9cbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByZW5kZXJlZENoaWxkcmVuLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBrZXkgPSBnZXRDaGlsZEtleShyZW5kZXJlZENoaWxkcmVuW2ldKTtcbiAgICAgICAgICAgIGlmICghcHJlc2VudEtleXMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgICAgICAgIGlmIChleGl0Q29tcGxldGUuZ2V0KGtleSkgIT09IHRydWUpIHtcbiAgICAgICAgICAgICAgICAgICAgZXhpdENvbXBsZXRlLnNldChrZXksIGZhbHNlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBleGl0Q29tcGxldGUuZGVsZXRlKGtleSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LCBbcmVuZGVyZWRDaGlsZHJlbiwgcHJlc2VudEtleXMubGVuZ3RoLCBwcmVzZW50S2V5cy5qb2luKFwiLVwiKV0pO1xuICAgIGNvbnN0IGV4aXRpbmdDaGlsZHJlbiA9IFtdO1xuICAgIGlmIChwcmVzZW50Q2hpbGRyZW4gIT09IGRpZmZlZENoaWxkcmVuKSB7XG4gICAgICAgIGxldCBuZXh0Q2hpbGRyZW4gPSBbLi4ucHJlc2VudENoaWxkcmVuXTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIExvb3AgdGhyb3VnaCBhbGwgdGhlIGN1cnJlbnRseSByZW5kZXJlZCBjb21wb25lbnRzIGFuZCBkZWNpZGUgd2hpY2hcbiAgICAgICAgICogYXJlIGV4aXRpbmcuXG4gICAgICAgICAqL1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJlbmRlcmVkQ2hpbGRyZW4ubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IGNoaWxkID0gcmVuZGVyZWRDaGlsZHJlbltpXTtcbiAgICAgICAgICAgIGNvbnN0IGtleSA9IGdldENoaWxkS2V5KGNoaWxkKTtcbiAgICAgICAgICAgIGlmICghcHJlc2VudEtleXMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgICAgICAgIG5leHRDaGlsZHJlbi5zcGxpY2UoaSwgMCwgY2hpbGQpO1xuICAgICAgICAgICAgICAgIGV4aXRpbmdDaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvKipcbiAgICAgICAgICogSWYgd2UncmUgaW4gXCJ3YWl0XCIgbW9kZSwgYW5kIHdlIGhhdmUgZXhpdGluZyBjaGlsZHJlbiwgd2Ugd2FudCB0b1xuICAgICAgICAgKiBvbmx5IHJlbmRlciB0aGVzZSB1bnRpbCB0aGV5J3ZlIGFsbCBleGl0ZWQuXG4gICAgICAgICAqL1xuICAgICAgICBpZiAobW9kZSA9PT0gXCJ3YWl0XCIgJiYgZXhpdGluZ0NoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgICAgICAgbmV4dENoaWxkcmVuID0gZXhpdGluZ0NoaWxkcmVuO1xuICAgICAgICB9XG4gICAgICAgIHNldFJlbmRlcmVkQ2hpbGRyZW4ob25seUVsZW1lbnRzKG5leHRDaGlsZHJlbikpO1xuICAgICAgICBzZXREaWZmZWRDaGlsZHJlbihwcmVzZW50Q2hpbGRyZW4pO1xuICAgICAgICAvKipcbiAgICAgICAgICogRWFybHkgcmV0dXJuIHRvIGVuc3VyZSBvbmNlIHdlJ3ZlIHNldCBzdGF0ZSB3aXRoIHRoZSBsYXRlc3QgZGlmZmVkXG4gICAgICAgICAqIGNoaWxkcmVuLCB3ZSBjYW4gaW1tZWRpYXRlbHkgcmUtcmVuZGVyLlxuICAgICAgICAgKi9cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgJiZcbiAgICAgICAgbW9kZSA9PT0gXCJ3YWl0XCIgJiZcbiAgICAgICAgcmVuZGVyZWRDaGlsZHJlbi5sZW5ndGggPiAxKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgWW91J3JlIGF0dGVtcHRpbmcgdG8gYW5pbWF0ZSBtdWx0aXBsZSBjaGlsZHJlbiB3aXRoaW4gQW5pbWF0ZVByZXNlbmNlLCBidXQgaXRzIG1vZGUgaXMgc2V0IHRvIFwid2FpdFwiLiBUaGlzIHdpbGwgbGVhZCB0byBvZGQgdmlzdWFsIGJlaGF2aW91ci5gKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSWYgd2UndmUgYmVlbiBwcm92aWRlZCBhIGZvcmNlUmVuZGVyIGZ1bmN0aW9uIGJ5IHRoZSBMYXlvdXRHcm91cENvbnRleHQsXG4gICAgICogd2UgY2FuIHVzZSBpdCB0byBmb3JjZSBhIHJlLXJlbmRlciBhbW9uZ3N0IGFsbCBzdXJyb3VuZGluZyBjb21wb25lbnRzIG9uY2VcbiAgICAgKiBhbGwgY29tcG9uZW50cyBoYXZlIGZpbmlzaGVkIGFuaW1hdGluZyBvdXQuXG4gICAgICovXG4gICAgY29uc3QgeyBmb3JjZVJlbmRlciB9ID0gdXNlQ29udGV4dChMYXlvdXRHcm91cENvbnRleHQpO1xuICAgIHJldHVybiAoanN4KEZyYWdtZW50LCB7IGNoaWxkcmVuOiByZW5kZXJlZENoaWxkcmVuLm1hcCgoY2hpbGQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGtleSA9IGdldENoaWxkS2V5KGNoaWxkKTtcbiAgICAgICAgICAgIGNvbnN0IGlzUHJlc2VudCA9IHByb3BhZ2F0ZSAmJiAhaXNQYXJlbnRQcmVzZW50XG4gICAgICAgICAgICAgICAgPyBmYWxzZVxuICAgICAgICAgICAgICAgIDogcHJlc2VudENoaWxkcmVuID09PSByZW5kZXJlZENoaWxkcmVuIHx8XG4gICAgICAgICAgICAgICAgICAgIHByZXNlbnRLZXlzLmluY2x1ZGVzKGtleSk7XG4gICAgICAgICAgICBjb25zdCBvbkV4aXQgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGV4aXRDb21wbGV0ZS5oYXMoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICBleGl0Q29tcGxldGUuc2V0KGtleSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxldCBpc0V2ZXJ5RXhpdENvbXBsZXRlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBleGl0Q29tcGxldGUuZm9yRWFjaCgoaXNFeGl0Q29tcGxldGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFpc0V4aXRDb21wbGV0ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzRXZlcnlFeGl0Q29tcGxldGUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoaXNFdmVyeUV4aXRDb21wbGV0ZSkge1xuICAgICAgICAgICAgICAgICAgICBmb3JjZVJlbmRlcj8uKCk7XG4gICAgICAgICAgICAgICAgICAgIHNldFJlbmRlcmVkQ2hpbGRyZW4ocGVuZGluZ1ByZXNlbnRDaGlsZHJlbi5jdXJyZW50KTtcbiAgICAgICAgICAgICAgICAgICAgcHJvcGFnYXRlICYmIHNhZmVUb1JlbW92ZT8uKCk7XG4gICAgICAgICAgICAgICAgICAgIG9uRXhpdENvbXBsZXRlICYmIG9uRXhpdENvbXBsZXRlKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiAoanN4KFByZXNlbmNlQ2hpbGQsIHsgaXNQcmVzZW50OiBpc1ByZXNlbnQsIGluaXRpYWw6ICFpc0luaXRpYWxSZW5kZXIuY3VycmVudCB8fCBpbml0aWFsXG4gICAgICAgICAgICAgICAgICAgID8gdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIDogZmFsc2UsIGN1c3RvbTogY3VzdG9tLCBwcmVzZW5jZUFmZmVjdHNMYXlvdXQ6IHByZXNlbmNlQWZmZWN0c0xheW91dCwgbW9kZTogbW9kZSwgb25FeGl0Q29tcGxldGU6IGlzUHJlc2VudCA/IHVuZGVmaW5lZCA6IG9uRXhpdCwgYW5jaG9yWDogYW5jaG9yWCwgY2hpbGRyZW46IGNoaWxkIH0sIGtleSkpO1xuICAgICAgICB9KSB9KSk7XG59O1xuXG5leHBvcnQgeyBBbmltYXRlUHJlc2VuY2UgfTtcbiJdLCJuYW1lcyI6WyJqc3giLCJGcmFnbWVudCIsInVzZU1lbW8iLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZUNvbnRleHQiLCJMYXlvdXRHcm91cENvbnRleHQiLCJ1c2VDb25zdGFudCIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJQcmVzZW5jZUNoaWxkIiwidXNlUHJlc2VuY2UiLCJvbmx5RWxlbWVudHMiLCJnZXRDaGlsZEtleSIsIkFuaW1hdGVQcmVzZW5jZSIsImNoaWxkcmVuIiwiY3VzdG9tIiwiaW5pdGlhbCIsIm9uRXhpdENvbXBsZXRlIiwicHJlc2VuY2VBZmZlY3RzTGF5b3V0IiwibW9kZSIsInByb3BhZ2F0ZSIsImFuY2hvclgiLCJpc1BhcmVudFByZXNlbnQiLCJzYWZlVG9SZW1vdmUiLCJwcmVzZW50Q2hpbGRyZW4iLCJwcmVzZW50S2V5cyIsIm1hcCIsImlzSW5pdGlhbFJlbmRlciIsInBlbmRpbmdQcmVzZW50Q2hpbGRyZW4iLCJleGl0Q29tcGxldGUiLCJNYXAiLCJkaWZmZWRDaGlsZHJlbiIsInNldERpZmZlZENoaWxkcmVuIiwicmVuZGVyZWRDaGlsZHJlbiIsInNldFJlbmRlcmVkQ2hpbGRyZW4iLCJjdXJyZW50IiwiaSIsImxlbmd0aCIsImtleSIsImluY2x1ZGVzIiwiZ2V0Iiwic2V0IiwiZGVsZXRlIiwiam9pbiIsImV4aXRpbmdDaGlsZHJlbiIsIm5leHRDaGlsZHJlbiIsImNoaWxkIiwic3BsaWNlIiwicHVzaCIsInByb2Nlc3MiLCJjb25zb2xlIiwid2FybiIsImZvcmNlUmVuZGVyIiwiaXNQcmVzZW50Iiwib25FeGl0IiwiaGFzIiwiaXNFdmVyeUV4aXRDb21wbGV0ZSIsImZvckVhY2giLCJpc0V4aXRDb21wbGV0ZSIsInVuZGVmaW5lZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChildKey: () => (/* binding */ getChildKey),\n/* harmony export */   onlyElements: () => (/* binding */ onlyElements)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nconst getChildKey = (child)=>child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child)=>{\n        if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) filtered.push(child);\n    });\n    return filtered;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUVqRCxNQUFNRSxjQUFjLENBQUNDLFFBQVVBLE1BQU1DLEdBQUcsSUFBSTtBQUM1QyxTQUFTQyxhQUFhQyxRQUFRO0lBQzFCLE1BQU1DLFdBQVcsRUFBRTtJQUNuQiwwRkFBMEY7SUFDMUZQLDJDQUFRQSxDQUFDUSxPQUFPLENBQUNGLFVBQVUsQ0FBQ0g7UUFDeEIsa0JBQUlGLHFEQUFjQSxDQUFDRSxRQUNmSSxTQUFTRSxJQUFJLENBQUNOO0lBQ3RCO0lBQ0EsT0FBT0k7QUFDWDtBQUVxQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkYXZpZFxcRGVza3RvcFxcUHJvamVrdHlcXGJha2FzYW5hX3Byb2RcXGJha2FzYW5hX3Byb2RcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGNvbXBvbmVudHNcXEFuaW1hdGVQcmVzZW5jZVxcdXRpbHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENoaWxkcmVuLCBpc1ZhbGlkRWxlbWVudCB9IGZyb20gJ3JlYWN0JztcblxuY29uc3QgZ2V0Q2hpbGRLZXkgPSAoY2hpbGQpID0+IGNoaWxkLmtleSB8fCBcIlwiO1xuZnVuY3Rpb24gb25seUVsZW1lbnRzKGNoaWxkcmVuKSB7XG4gICAgY29uc3QgZmlsdGVyZWQgPSBbXTtcbiAgICAvLyBXZSB1c2UgZm9yRWFjaCBoZXJlIGluc3RlYWQgb2YgbWFwIGFzIG1hcCBtdXRhdGVzIHRoZSBjb21wb25lbnQga2V5IGJ5IHByZXByZW5kaW5nIGAuJGBcbiAgICBDaGlsZHJlbi5mb3JFYWNoKGNoaWxkcmVuLCAoY2hpbGQpID0+IHtcbiAgICAgICAgaWYgKGlzVmFsaWRFbGVtZW50KGNoaWxkKSlcbiAgICAgICAgICAgIGZpbHRlcmVkLnB1c2goY2hpbGQpO1xuICAgIH0pO1xuICAgIHJldHVybiBmaWx0ZXJlZDtcbn1cblxuZXhwb3J0IHsgZ2V0Q2hpbGRLZXksIG9ubHlFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbIkNoaWxkcmVuIiwiaXNWYWxpZEVsZW1lbnQiLCJnZXRDaGlsZEtleSIsImNoaWxkIiwia2V5Iiwib25seUVsZW1lbnRzIiwiY2hpbGRyZW4iLCJmaWx0ZXJlZCIsImZvckVhY2giLCJwdXNoIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_MinimalistHero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MinimalistHero */ \"(app-pages-browser)/./src/components/MinimalistHero.jsx\");\n/* harmony import */ var _components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/UnifiedTypography */ \"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx\");\n/* harmony import */ var _components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/UnifiedCard */ \"(app-pages-browser)/./src/components/ui/UnifiedCard.jsx\");\n/* harmony import */ var _components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/UnifiedButton */ \"(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\");\n/* harmony import */ var _components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/IconSystem */ \"(app-pages-browser)/./src/components/ui/IconSystem.jsx\");\n/* harmony import */ var _components_LuxuryTestimonials__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/LuxuryTestimonials */ \"(app-pages-browser)/./src/components/LuxuryTestimonials.jsx\");\n/* harmony import */ var _components_InvestmentSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/InvestmentSection */ \"(app-pages-browser)/./src/components/InvestmentSection.jsx\");\n/* harmony import */ var _components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/LuxuryElements */ \"(app-pages-browser)/./src/components/LuxuryElements.jsx\");\n/* harmony import */ var _components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ScrollReveal */ \"(app-pages-browser)/./src/components/ScrollReveal.jsx\");\n/* harmony import */ var _components_LuxuryWhatsApp__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/LuxuryWhatsApp */ \"(app-pages-browser)/./src/components/LuxuryWhatsApp.jsx\");\n/* harmony import */ var _components_LuxuryScrollProgress__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/LuxuryScrollProgress */ \"(app-pages-browser)/./src/components/LuxuryScrollProgress.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// Structured data for homepage SEO\nconst homepageStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"BAKASANA\",\n    \"alternateName\": \"Retreaty Jogi Bali Sri Lanka\",\n    \"url\": \"https://bakasana-travel.blog\",\n    \"logo\": \"https://bakasana-travel.blog/images/logo/bakasana-logo.png\",\n    \"description\": \"Najlepsze retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz\",\n    \"foundingDate\": \"2020\",\n    \"founder\": {\n        \"@type\": \"Person\",\n        \"name\": \"Julia Jakubowicz\",\n        \"jobTitle\": \"Instruktorka Jogi i Fizjoterapeutka\"\n    },\n    \"aggregateRating\": {\n        \"@type\": \"AggregateRating\",\n        \"ratingValue\": \"4.9\",\n        \"reviewCount\": \"150\",\n        \"bestRating\": \"5\"\n    },\n    \"priceRange\": \"2900-4200 PLN\"\n};\nconst testimonialsStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"itemListElement\": [\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Anna K.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Retreat na Bali z Julią był przełomowym momentem w moim życiu. Profesjonalne podejście połączone z ciepłem i autentycznością.\"\n        },\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Marta W.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Małe grupy, indywidualne podejście i magiczne miejsca. Sri Lanka z BAKASANA to doświadczenie, które zmienia perspektywę.\"\n        },\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Karolina M.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Julia łączy wiedzę fizjoterapeuty z duchowością jogi. Zajęcia online przygotowały mnie idealnie na retreat.\"\n        }\n    ]\n};\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(homepageStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(testimonialsStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MinimalistHero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-linen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-container-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                children: \"Odkryj magię jogi w duchowej Azji\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.LeadText, {\n                                children: \"Jestem Julia - certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka z 7-letnim doświadczeniem. Tworzę autentyczne retreaty na Bali i Sri Lanka, kt\\xf3re łączą głęboką praktykę jogi z odkrywaniem duchowego dziedzictwa Azji w małych, kameralnych grupach.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-sanctuary relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.02]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C19B68' fill-opacity='0.1'%3E%3Cpath d='M40 40c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                backgroundSize: '80px 80px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-container-sm relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.ScrollReveal, {\n                                variant: \"fadeInUp\",\n                                className: \"text-center mb-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                    children: \"Nasze Usługi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerContainer, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"lotus\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Retreaty na Bali\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"7-dniowe transformacyjne podr\\xf3że w Ubud. Praktyka jogi wśr\\xf3d taras\\xf3w ryżowych, medytacja w świątyniach, ayurveda i autentyczne poznawanie balińskiej kultury.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/retreaty\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Poznaj program\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"mountain\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Retreaty na Sri Lanka\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"10-dniowa duchowa podr\\xf3ż przez perłę Oceanu Indyjskiego. Joga na plażach, medytacja w g\\xf3rach, ayurveda i odkrywanie buddyjskich tradycji.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/retreaty\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Poznaj program\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"home\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Zajęcia Stacjonarne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Zajęcia jogi i fitness w przytulnym studio w Warszawie. Małe grupy, profesjonalna opieka i zapisy online przez system Fitssey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-stacjonarne\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Zapisz się na zajęcia\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"monitor\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Zajęcia Online\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Regularne sesje jogi online w małych grupach. Indywidualne podejście, korekty postawy i przygotowanie do retreat\\xf3w w Azji.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-online\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Dołącz do grupy\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"user\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Konsultacje Indywidualne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Sesje terapeutyczne i konsultacje dostosowane do Twoich indywidualnych potrzeb. Zapisy przez system Fitssey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-stacjonarne#konsultacje\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Um\\xf3w konsultację\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryTestimonials__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxurySeparator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                showOnlyRetreats: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-linen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-container-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                    children: \"Najczęściej zadawane pytania\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                    className: \"text-sage max-w-2xl mx-auto\",\n                                    children: \"Odpowiedzi na najważniejsze pytania dotyczące retreat\\xf3w jogi na Bali i Sri Lanka\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-md\",\n                            itemScope: true,\n                            itemType: \"https://schema.org/FAQPage\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm /* TODO: Replace with CardTitle */\",\n                                            itemProp: \"name\",\n                                            children: \"Czy retreaty są odpowiednie dla początkujących?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Tak! Nasze retreaty są dostosowane do wszystkich poziom\\xf3w zaawansowania. Julia jako doświadczona instruktorka zapewnia modyfikacje ćwiczeń i indywidualne podejście do każdego uczestnika.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm\",\n                                            itemProp: \"name\",\n                                            children: \"Ile os\\xf3b uczestniczy w retreatach?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Nasze grupy są małe i kameralne - maksymalnie 10-12 os\\xf3b. Dzięki temu każdy uczestnik otrzymuje indywidualną uwagę i może w pełni doświadczyć transformacyjnej mocy jogi.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm\",\n                                            itemProp: \"name\",\n                                            children: \"Co jest wliczone w cenę retreatu?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Cena obejmuje zakwaterowanie, wszystkie posiłki, codzienne sesje jogi, medytację, wycieczki kulturalne, transport lokalny oraz opiekę doświadczonej instruktorki przez cały pobyt.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LuxuryScrollProgress.jsx":
/*!*************************************************!*\
  !*** ./src/components/LuxuryScrollProgress.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MinimalScrollProgress: () => (/* binding */ MinimalScrollProgress),\n/* harmony export */   SectionScrollProgress: () => (/* binding */ SectionScrollProgress),\n/* harmony export */   \"default\": () => (/* binding */ LuxuryScrollProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default,SectionScrollProgress,MinimalScrollProgress auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n/**\r\n * LuxuryScrollProgress - Luksusowy pasek postępu przewijania\r\n * Z subtelnym złotym gradientem i płynną animacją\r\n */ function LuxuryScrollProgress(param) {\n    let { height = 3, showPercentage = false, goldAccent = true, className = '' } = param;\n    _s();\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LuxuryScrollProgress.useEffect\": ()=>{\n            const updateScrollProgress = {\n                \"LuxuryScrollProgress.useEffect.updateScrollProgress\": ()=>{\n                    if (true) {\n                        const scrolled = window.scrollY;\n                        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;\n                        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;\n                        setScrollProgress(progress);\n                        setIsVisible(scrolled > 100); // Pokaż po przewinięciu 100px\n                    }\n                }\n            }[\"LuxuryScrollProgress.useEffect.updateScrollProgress\"];\n            if (true) {\n                window.addEventListener('scroll', updateScrollProgress, {\n                    passive: true\n                });\n                updateScrollProgress(); // Initial call\n                return ({\n                    \"LuxuryScrollProgress.useEffect\": ()=>window.removeEventListener('scroll', updateScrollProgress)\n                })[\"LuxuryScrollProgress.useEffect\"];\n            }\n        }\n    }[\"LuxuryScrollProgress.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"fixed top-0 left-0 right-0 z-40 \".concat(className),\n        initial: {\n            opacity: 0,\n            y: -height\n        },\n        animate: {\n            opacity: isVisible ? 1 : 0,\n            y: isVisible ? 0 : -height\n        },\n        transition: {\n            duration: 0.3,\n            ease: \"easeOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-stone-light/20 backdrop-blur-sm\",\n                style: {\n                    height: \"\".concat(height, \"px\")\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"h-full \".concat(goldAccent ? 'bg-gradient-to-r from-enterprise-brown via-terra to-sand' : 'bg-charcoal'),\n                        style: {\n                            scaleX: scrollProgress,\n                            transformOrigin: '0%'\n                        },\n                        transition: {\n                            duration: 0.1,\n                            ease: \"easeOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    goldAccent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-0 left-0 h-full bg-gradient-to-r from-enterprise-brown/30 via-terra/30 to-sand/30 blur-sm\",\n                        style: {\n                            scaleX: scrollProgress,\n                            transformOrigin: '0%',\n                            width: '100%'\n                        },\n                        transition: {\n                            duration: 0.1,\n                            ease: \"easeOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            showPercentage && isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-full right-4 mt-2 px-3 py-1 bg-sanctuary/90 backdrop-blur-sm rounded-full shadow-elegant border border-enterprise-brown/10\",\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs font-inter font-medium text-enterprise-brown\",\n                    children: [\n                        Math.round(scrollProgress * 100),\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(LuxuryScrollProgress, \"FejIHpCXBB29KNLTJCmJijLQ/x8=\");\n_c = LuxuryScrollProgress;\n// Wariant z sekcjami\nfunction SectionScrollProgress(param) {\n    let { sections = [], goldAccent = true, className = '' } = param;\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SectionScrollProgress.useEffect\": ()=>{\n            const updateProgress = {\n                \"SectionScrollProgress.useEffect.updateProgress\": ()=>{\n                    if (true) {\n                        const scrolled = window.scrollY;\n                        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;\n                        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;\n                        setScrollProgress(progress);\n                        // Determine active section\n                        const sectionElements = sections.map({\n                            \"SectionScrollProgress.useEffect.updateProgress.sectionElements\": (section)=>document.getElementById(section.id)\n                        }[\"SectionScrollProgress.useEffect.updateProgress.sectionElements\"]).filter(Boolean);\n                        let currentSection = 0;\n                        sectionElements.forEach({\n                            \"SectionScrollProgress.useEffect.updateProgress\": (element, index)=>{\n                                if (element) {\n                                    const rect = element.getBoundingClientRect();\n                                    if (rect.top <= window.innerHeight / 2) {\n                                        currentSection = index;\n                                    }\n                                }\n                            }\n                        }[\"SectionScrollProgress.useEffect.updateProgress\"]);\n                        setActiveSection(currentSection);\n                    }\n                }\n            }[\"SectionScrollProgress.useEffect.updateProgress\"];\n            if (true) {\n                window.addEventListener('scroll', updateProgress, {\n                    passive: true\n                });\n                updateProgress();\n                return ({\n                    \"SectionScrollProgress.useEffect\": ()=>window.removeEventListener('scroll', updateProgress)\n                })[\"SectionScrollProgress.useEffect\"];\n            }\n        }\n    }[\"SectionScrollProgress.useEffect\"], [\n        sections\n    ]);\n    if (sections.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryScrollProgress, {\n            goldAccent: goldAccent,\n            className: className\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n            lineNumber: 146,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"fixed top-0 left-0 right-0 z-40 \".concat(className),\n        initial: {\n            opacity: 0,\n            y: -4\n        },\n        animate: {\n            opacity: scrollProgress > 0.05 ? 1 : 0,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-1 bg-stone-light/20 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"h-full \".concat(goldAccent ? 'bg-gradient-to-r from-enterprise-brown via-terra to-sand' : 'bg-charcoal'),\n                    style: {\n                        scaleX: scrollProgress,\n                        transformOrigin: '0%'\n                    },\n                    transition: {\n                        duration: 0.1\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 flex justify-center mt-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2 px-4 py-2 bg-sanctuary/90 backdrop-blur-sm rounded-full shadow-elegant border border-enterprise-brown/10\",\n                    children: sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(index === activeSection ? goldAccent ? 'bg-enterprise-brown scale-125' : 'bg-charcoal scale-125' : 'bg-stone-light hover:bg-stone'),\n                            onClick: ()=>{\n                                const element = document.getElementById(section.id);\n                                if (element) {\n                                    element.scrollIntoView({\n                                        behavior: 'smooth'\n                                    });\n                                }\n                            },\n                            whileHover: {\n                                scale: 1.2\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            title: section.title\n                        }, section.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s1(SectionScrollProgress, \"RNjm9+hsSIZWc25WrY3K0Z1no14=\");\n_c1 = SectionScrollProgress;\n// Wariant minimalny\nfunction MinimalScrollProgress(param) {\n    let { goldAccent = true } = param;\n    _s2();\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MinimalScrollProgress.useEffect\": ()=>{\n            const updateProgress = {\n                \"MinimalScrollProgress.useEffect.updateProgress\": ()=>{\n                    if (true) {\n                        const scrolled = window.scrollY;\n                        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;\n                        const progress = maxScroll > 0 ? scrolled / maxScroll : 0;\n                        setScrollProgress(progress);\n                    }\n                }\n            }[\"MinimalScrollProgress.useEffect.updateProgress\"];\n            if (true) {\n                window.addEventListener('scroll', updateProgress, {\n                    passive: true\n                });\n                updateProgress();\n                return ({\n                    \"MinimalScrollProgress.useEffect\": ()=>window.removeEventListener('scroll', updateProgress)\n                })[\"MinimalScrollProgress.useEffect\"];\n            }\n        }\n    }[\"MinimalScrollProgress.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 h-0.5 z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            className: \"h-full \".concat(goldAccent ? 'bg-gradient-to-r from-enterprise-brown to-terra' : 'bg-charcoal'),\n            style: {\n                scaleX: scrollProgress,\n                transformOrigin: '0%'\n            },\n            transition: {\n                duration: 0.1\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryScrollProgress.jsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s2(MinimalScrollProgress, \"mI/zHsdmTwVZEMJ2l0wkvaaiYnI=\");\n_c2 = MinimalScrollProgress;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"LuxuryScrollProgress\");\n$RefreshReg$(_c1, \"SectionScrollProgress\");\n$RefreshReg$(_c2, \"MinimalScrollProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0x1eHVyeVNjcm9sbFByb2dyZXNzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDWjtBQUV2Qzs7O0NBR0MsR0FFYyxTQUFTSSxxQkFBcUIsS0FLNUM7UUFMNEMsRUFDM0NDLFNBQVMsQ0FBQyxFQUNWQyxpQkFBaUIsS0FBSyxFQUN0QkMsYUFBYSxJQUFJLEVBQ2pCQyxZQUFZLEVBQUUsRUFDZixHQUw0Qzs7SUFNM0MsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHVCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNVLFdBQVdDLGFBQWEsR0FBR1gsK0NBQVFBLENBQUM7SUFFM0NDLGdEQUFTQTswQ0FBQztZQUNSLE1BQU1XO3VFQUF1QjtvQkFDM0IsSUFBSSxJQUE2QixFQUFFO3dCQUNqQyxNQUFNQyxXQUFXQyxPQUFPQyxPQUFPO3dCQUMvQixNQUFNQyxZQUFZQyxTQUFTQyxlQUFlLENBQUNDLFlBQVksR0FBR0wsT0FBT00sV0FBVzt3QkFDNUUsTUFBTUMsV0FBV0wsWUFBWSxJQUFJSCxXQUFXRyxZQUFZO3dCQUV4RFAsa0JBQWtCWTt3QkFDbEJWLGFBQWFFLFdBQVcsTUFBTSw4QkFBOEI7b0JBQzlEO2dCQUNGOztZQUVBLElBQUksSUFBNkIsRUFBRTtnQkFDakNDLE9BQU9RLGdCQUFnQixDQUFDLFVBQVVWLHNCQUFzQjtvQkFBRVcsU0FBUztnQkFBSztnQkFDeEVYLHdCQUF3QixlQUFlO2dCQUV2QztzREFBTyxJQUFNRSxPQUFPVSxtQkFBbUIsQ0FBQyxVQUFVWjs7WUFDcEQ7UUFDRjt5Q0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNWLCtFQUFNQSxDQUFDdUIsR0FBRztRQUNUbEIsV0FBVyxtQ0FBNkMsT0FBVkE7UUFDOUNtQixTQUFTO1lBQUVDLFNBQVM7WUFBR0MsR0FBRyxDQUFDeEI7UUFBTztRQUNsQ3lCLFNBQVM7WUFDUEYsU0FBU2pCLFlBQVksSUFBSTtZQUN6QmtCLEdBQUdsQixZQUFZLElBQUksQ0FBQ047UUFDdEI7UUFDQTBCLFlBQVk7WUFBRUMsVUFBVTtZQUFLQyxNQUFNO1FBQVU7OzBCQUc3Qyw4REFBQ1A7Z0JBQ0NsQixXQUFVO2dCQUNWMEIsT0FBTztvQkFBRTdCLFFBQVEsR0FBVSxPQUFQQSxRQUFPO2dCQUFJOztrQ0FHL0IsOERBQUNGLCtFQUFNQSxDQUFDdUIsR0FBRzt3QkFDVGxCLFdBQVcsVUFJVixPQUhDRCxhQUNJLDZEQUNBO3dCQUVOMkIsT0FBTzs0QkFDTEMsUUFBUTFCOzRCQUNSMkIsaUJBQWlCO3dCQUNuQjt3QkFDQUwsWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0MsTUFBTTt3QkFBVTs7Ozs7O29CQUk5QzFCLDRCQUNDLDhEQUFDSiwrRUFBTUEsQ0FBQ3VCLEdBQUc7d0JBQ1RsQixXQUFVO3dCQUNWMEIsT0FBTzs0QkFDTEMsUUFBUTFCOzRCQUNSMkIsaUJBQWlCOzRCQUNqQkMsT0FBTzt3QkFDVDt3QkFDQU4sWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0MsTUFBTTt3QkFBVTs7Ozs7Ozs7Ozs7O1lBTWxEM0Isa0JBQWtCSywyQkFDakIsOERBQUNSLCtFQUFNQSxDQUFDdUIsR0FBRztnQkFDVGxCLFdBQVU7Z0JBQ1ZtQixTQUFTO29CQUFFQyxTQUFTO29CQUFHVSxPQUFPO2dCQUFJO2dCQUNsQ1IsU0FBUztvQkFBRUYsU0FBUztvQkFBR1UsT0FBTztnQkFBRTtnQkFDaENQLFlBQVk7b0JBQUVDLFVBQVU7b0JBQUtPLE9BQU87Z0JBQUk7MEJBRXhDLDRFQUFDQztvQkFBS2hDLFdBQVU7O3dCQUNiaUMsS0FBS0MsS0FBSyxDQUFDakMsaUJBQWlCO3dCQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNOUM7R0F2RndCTDtLQUFBQTtBQXlGeEIscUJBQXFCO0FBQ2QsU0FBU3VDLHNCQUFzQixLQUlyQztRQUpxQyxFQUNwQ0MsV0FBVyxFQUFFLEVBQ2JyQyxhQUFhLElBQUksRUFDakJDLFlBQVksRUFBRSxFQUNmLEdBSnFDOztJQUtwQyxNQUFNLENBQUNxQyxlQUFlQyxpQkFBaUIsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ1EsZ0JBQWdCQyxrQkFBa0IsR0FBR1QsK0NBQVFBLENBQUM7SUFFckRDLGdEQUFTQTsyQ0FBQztZQUNSLE1BQU02QztrRUFBaUI7b0JBQ3JCLElBQUksSUFBNkIsRUFBRTt3QkFDakMsTUFBTWpDLFdBQVdDLE9BQU9DLE9BQU87d0JBQy9CLE1BQU1DLFlBQVlDLFNBQVNDLGVBQWUsQ0FBQ0MsWUFBWSxHQUFHTCxPQUFPTSxXQUFXO3dCQUM1RSxNQUFNQyxXQUFXTCxZQUFZLElBQUlILFdBQVdHLFlBQVk7d0JBRXhEUCxrQkFBa0JZO3dCQUVsQiwyQkFBMkI7d0JBQzNCLE1BQU0wQixrQkFBa0JKLFNBQVNLLEdBQUc7OEZBQUNDLENBQUFBLFVBQ25DaEMsU0FBU2lDLGNBQWMsQ0FBQ0QsUUFBUUUsRUFBRTs2RkFDbENDLE1BQU0sQ0FBQ0M7d0JBRVQsSUFBSUMsaUJBQWlCO3dCQUNyQlAsZ0JBQWdCUSxPQUFPOzhFQUFDLENBQUNDLFNBQVNDO2dDQUNoQyxJQUFJRCxTQUFTO29DQUNYLE1BQU1FLE9BQU9GLFFBQVFHLHFCQUFxQjtvQ0FDMUMsSUFBSUQsS0FBS0UsR0FBRyxJQUFJOUMsT0FBT00sV0FBVyxHQUFHLEdBQUc7d0NBQ3RDa0MsaUJBQWlCRztvQ0FDbkI7Z0NBQ0Y7NEJBQ0Y7O3dCQUVBWixpQkFBaUJTO29CQUNuQjtnQkFDRjs7WUFFQSxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDeEMsT0FBT1EsZ0JBQWdCLENBQUMsVUFBVXdCLGdCQUFnQjtvQkFBRXZCLFNBQVM7Z0JBQUs7Z0JBQ2xFdUI7Z0JBRUE7dURBQU8sSUFBTWhDLE9BQU9VLG1CQUFtQixDQUFDLFVBQVVzQjs7WUFDcEQ7UUFDRjswQ0FBRztRQUFDSDtLQUFTO0lBRWIsSUFBSUEsU0FBU2tCLE1BQU0sS0FBSyxHQUFHO1FBQ3pCLHFCQUFPLDhEQUFDMUQ7WUFBcUJHLFlBQVlBO1lBQVlDLFdBQVdBOzs7Ozs7SUFDbEU7SUFFQSxxQkFDRSw4REFBQ0wsK0VBQU1BLENBQUN1QixHQUFHO1FBQ1RsQixXQUFXLG1DQUE2QyxPQUFWQTtRQUM5Q21CLFNBQVM7WUFBRUMsU0FBUztZQUFHQyxHQUFHLENBQUM7UUFBRTtRQUM3QkMsU0FBUztZQUFFRixTQUFTbkIsaUJBQWlCLE9BQU8sSUFBSTtZQUFHb0IsR0FBRztRQUFFO1FBQ3hERSxZQUFZO1lBQUVDLFVBQVU7UUFBSTs7MEJBRzVCLDhEQUFDTjtnQkFBSWxCLFdBQVU7MEJBQ2IsNEVBQUNMLCtFQUFNQSxDQUFDdUIsR0FBRztvQkFDVGxCLFdBQVcsVUFJVixPQUhDRCxhQUNJLDZEQUNBO29CQUVOMkIsT0FBTzt3QkFDTEMsUUFBUTFCO3dCQUNSMkIsaUJBQWlCO29CQUNuQjtvQkFDQUwsWUFBWTt3QkFBRUMsVUFBVTtvQkFBSTs7Ozs7Ozs7Ozs7MEJBS2hDLDhEQUFDTjtnQkFBSWxCLFdBQVU7MEJBQ2IsNEVBQUNrQjtvQkFBSWxCLFdBQVU7OEJBQ1pvQyxTQUFTSyxHQUFHLENBQUMsQ0FBQ0MsU0FBU1Esc0JBQ3RCLDhEQUFDdkQsK0VBQU1BLENBQUM0RCxNQUFNOzRCQUVadkQsV0FBVyxvREFNVixPQUxDa0QsVUFBVWIsZ0JBQ050QyxhQUNFLGtDQUNBLDBCQUNGOzRCQUVOeUQsU0FBUztnQ0FDUCxNQUFNUCxVQUFVdkMsU0FBU2lDLGNBQWMsQ0FBQ0QsUUFBUUUsRUFBRTtnQ0FDbEQsSUFBSUssU0FBUztvQ0FDWEEsUUFBUVEsY0FBYyxDQUFDO3dDQUFFQyxVQUFVO29DQUFTO2dDQUM5Qzs0QkFDRjs0QkFDQUMsWUFBWTtnQ0FBRTdCLE9BQU87NEJBQUk7NEJBQ3pCOEIsVUFBVTtnQ0FBRTlCLE9BQU87NEJBQUk7NEJBQ3ZCK0IsT0FBT25CLFFBQVFtQixLQUFLOzJCQWhCZm5CLFFBQVFFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXVCN0I7SUFuR2dCVDtNQUFBQTtBQXFHaEIsb0JBQW9CO0FBQ2IsU0FBUzJCLHNCQUFzQixLQUFxQjtRQUFyQixFQUFFL0QsYUFBYSxJQUFJLEVBQUUsR0FBckI7O0lBQ3BDLE1BQU0sQ0FBQ0UsZ0JBQWdCQyxrQkFBa0IsR0FBR1QsK0NBQVFBLENBQUM7SUFFckRDLGdEQUFTQTsyQ0FBQztZQUNSLE1BQU02QztrRUFBaUI7b0JBQ3JCLElBQUksSUFBNkIsRUFBRTt3QkFDakMsTUFBTWpDLFdBQVdDLE9BQU9DLE9BQU87d0JBQy9CLE1BQU1DLFlBQVlDLFNBQVNDLGVBQWUsQ0FBQ0MsWUFBWSxHQUFHTCxPQUFPTSxXQUFXO3dCQUM1RSxNQUFNQyxXQUFXTCxZQUFZLElBQUlILFdBQVdHLFlBQVk7d0JBQ3hEUCxrQkFBa0JZO29CQUNwQjtnQkFDRjs7WUFFQSxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDUCxPQUFPUSxnQkFBZ0IsQ0FBQyxVQUFVd0IsZ0JBQWdCO29CQUFFdkIsU0FBUztnQkFBSztnQkFDbEV1QjtnQkFFQTt1REFBTyxJQUFNaEMsT0FBT1UsbUJBQW1CLENBQUMsVUFBVXNCOztZQUNwRDtRQUNGOzBDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ3JCO1FBQUlsQixXQUFVO2tCQUNiLDRFQUFDTCwrRUFBTUEsQ0FBQ3VCLEdBQUc7WUFDVGxCLFdBQVcsVUFJVixPQUhDRCxhQUNJLG9EQUNBO1lBRU4yQixPQUFPO2dCQUNMQyxRQUFRMUI7Z0JBQ1IyQixpQkFBaUI7WUFDbkI7WUFDQUwsWUFBWTtnQkFBRUMsVUFBVTtZQUFJOzs7Ozs7Ozs7OztBQUlwQztJQXJDZ0JzQztNQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkYXZpZFxcRGVza3RvcFxcUHJvamVrdHlcXGJha2FzYW5hX3Byb2RcXGJha2FzYW5hX3Byb2RcXHNyY1xcY29tcG9uZW50c1xcTHV4dXJ5U2Nyb2xsUHJvZ3Jlc3MuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcclxuXHJcbi8qKlxyXG4gKiBMdXh1cnlTY3JvbGxQcm9ncmVzcyAtIEx1a3N1c293eSBwYXNlayBwb3N0xJlwdSBwcnpld2lqYW5pYVxyXG4gKiBaIHN1YnRlbG55bSB6xYJvdHltIGdyYWRpZW50ZW0gaSBwxYJ5bm7EhSBhbmltYWNqxIVcclxuICovXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMdXh1cnlTY3JvbGxQcm9ncmVzcyh7IFxyXG4gIGhlaWdodCA9IDMsXHJcbiAgc2hvd1BlcmNlbnRhZ2UgPSBmYWxzZSxcclxuICBnb2xkQWNjZW50ID0gdHJ1ZSxcclxuICBjbGFzc05hbWUgPSAnJ1xyXG59KSB7XHJcbiAgY29uc3QgW3Njcm9sbFByb2dyZXNzLCBzZXRTY3JvbGxQcm9ncmVzc10gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbaXNWaXNpYmxlLCBzZXRJc1Zpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgdXBkYXRlU2Nyb2xsUHJvZ3Jlc3MgPSAoKSA9PiB7XHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIGNvbnN0IHNjcm9sbGVkID0gd2luZG93LnNjcm9sbFk7XHJcbiAgICAgICAgY29uc3QgbWF4U2Nyb2xsID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbEhlaWdodCAtIHdpbmRvdy5pbm5lckhlaWdodDtcclxuICAgICAgICBjb25zdCBwcm9ncmVzcyA9IG1heFNjcm9sbCA+IDAgPyBzY3JvbGxlZCAvIG1heFNjcm9sbCA6IDA7XHJcbiAgICAgICAgXHJcbiAgICAgICAgc2V0U2Nyb2xsUHJvZ3Jlc3MocHJvZ3Jlc3MpO1xyXG4gICAgICAgIHNldElzVmlzaWJsZShzY3JvbGxlZCA+IDEwMCk7IC8vIFBva2HFvCBwbyBwcnpld2luacSZY2l1IDEwMHB4XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCB1cGRhdGVTY3JvbGxQcm9ncmVzcywgeyBwYXNzaXZlOiB0cnVlIH0pO1xyXG4gICAgICB1cGRhdGVTY3JvbGxQcm9ncmVzcygpOyAvLyBJbml0aWFsIGNhbGxcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgdXBkYXRlU2Nyb2xsUHJvZ3Jlc3MpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgIGNsYXNzTmFtZT17YGZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNDAgJHtjbGFzc05hbWV9YH1cclxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtaGVpZ2h0IH19XHJcbiAgICAgIGFuaW1hdGU9e3sgXHJcbiAgICAgICAgb3BhY2l0eTogaXNWaXNpYmxlID8gMSA6IDAsXHJcbiAgICAgICAgeTogaXNWaXNpYmxlID8gMCA6IC1oZWlnaHRcclxuICAgICAgfX1cclxuICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBlYXNlOiBcImVhc2VPdXRcIiB9fVxyXG4gICAgPlxyXG4gICAgICB7LyogQmFja2dyb3VuZCAqL31cclxuICAgICAgPGRpdiBcclxuICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctc3RvbmUtbGlnaHQvMjAgYmFja2Ryb3AtYmx1ci1zbVwiXHJcbiAgICAgICAgc3R5bGU9e3sgaGVpZ2h0OiBgJHtoZWlnaHR9cHhgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7LyogUHJvZ3Jlc3MgYmFyICovfVxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgJHtcclxuICAgICAgICAgICAgZ29sZEFjY2VudCBcclxuICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tZW50ZXJwcmlzZS1icm93biB2aWEtdGVycmEgdG8tc2FuZCcgXHJcbiAgICAgICAgICAgICAgOiAnYmctY2hhcmNvYWwnXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgICAgIHN0eWxlPXt7IFxyXG4gICAgICAgICAgICBzY2FsZVg6IHNjcm9sbFByb2dyZXNzLFxyXG4gICAgICAgICAgICB0cmFuc2Zvcm1PcmlnaW46ICcwJSdcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjEsIGVhc2U6IFwiZWFzZU91dFwiIH19XHJcbiAgICAgICAgLz5cclxuXHJcbiAgICAgICAgey8qIFN1YnRsZSBnbG93IGVmZmVjdCAqL31cclxuICAgICAgICB7Z29sZEFjY2VudCAmJiAoXHJcbiAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgaC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbnRlcnByaXNlLWJyb3duLzMwIHZpYS10ZXJyYS8zMCB0by1zYW5kLzMwIGJsdXItc21cIlxyXG4gICAgICAgICAgICBzdHlsZT17eyBcclxuICAgICAgICAgICAgICBzY2FsZVg6IHNjcm9sbFByb2dyZXNzLFxyXG4gICAgICAgICAgICAgIHRyYW5zZm9ybU9yaWdpbjogJzAlJyxcclxuICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMSwgZWFzZTogXCJlYXNlT3V0XCIgfX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogUGVyY2VudGFnZSBpbmRpY2F0b3IgKi99XHJcbiAgICAgIHtzaG93UGVyY2VudGFnZSAmJiBpc1Zpc2libGUgJiYgKFxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCByaWdodC00IG10LTIgcHgtMyBweS0xIGJnLXNhbmN0dWFyeS85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCBzaGFkb3ctZWxlZ2FudCBib3JkZXIgYm9yZGVyLWVudGVycHJpc2UtYnJvd24vMTBcIlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XHJcbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMsIGRlbGF5OiAwLjEgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtaW50ZXIgZm9udC1tZWRpdW0gdGV4dC1lbnRlcnByaXNlLWJyb3duXCI+XHJcbiAgICAgICAgICAgIHtNYXRoLnJvdW5kKHNjcm9sbFByb2dyZXNzICogMTAwKX0lXHJcbiAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC9tb3Rpb24uZGl2PlxyXG4gICk7XHJcbn1cclxuXHJcbi8vIFdhcmlhbnQgeiBzZWtjamFtaVxyXG5leHBvcnQgZnVuY3Rpb24gU2VjdGlvblNjcm9sbFByb2dyZXNzKHsgXHJcbiAgc2VjdGlvbnMgPSBbXSxcclxuICBnb2xkQWNjZW50ID0gdHJ1ZSxcclxuICBjbGFzc05hbWUgPSAnJ1xyXG59KSB7XHJcbiAgY29uc3QgW2FjdGl2ZVNlY3Rpb24sIHNldEFjdGl2ZVNlY3Rpb25dID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW3Njcm9sbFByb2dyZXNzLCBzZXRTY3JvbGxQcm9ncmVzc10gPSB1c2VTdGF0ZSgwKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHVwZGF0ZVByb2dyZXNzID0gKCkgPT4ge1xyXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICBjb25zdCBzY3JvbGxlZCA9IHdpbmRvdy5zY3JvbGxZO1xyXG4gICAgICAgIGNvbnN0IG1heFNjcm9sbCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxIZWlnaHQgLSB3aW5kb3cuaW5uZXJIZWlnaHQ7XHJcbiAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSBtYXhTY3JvbGwgPiAwID8gc2Nyb2xsZWQgLyBtYXhTY3JvbGwgOiAwO1xyXG4gICAgICAgIFxyXG4gICAgICAgIHNldFNjcm9sbFByb2dyZXNzKHByb2dyZXNzKTtcclxuXHJcbiAgICAgICAgLy8gRGV0ZXJtaW5lIGFjdGl2ZSBzZWN0aW9uXHJcbiAgICAgICAgY29uc3Qgc2VjdGlvbkVsZW1lbnRzID0gc2VjdGlvbnMubWFwKHNlY3Rpb24gPT4gXHJcbiAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChzZWN0aW9uLmlkKVxyXG4gICAgICAgICkuZmlsdGVyKEJvb2xlYW4pO1xyXG5cclxuICAgICAgICBsZXQgY3VycmVudFNlY3Rpb24gPSAwO1xyXG4gICAgICAgIHNlY3Rpb25FbGVtZW50cy5mb3JFYWNoKChlbGVtZW50LCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgaWYgKGVsZW1lbnQpIHtcclxuICAgICAgICAgICAgY29uc3QgcmVjdCA9IGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgICAgICAgIGlmIChyZWN0LnRvcCA8PSB3aW5kb3cuaW5uZXJIZWlnaHQgLyAyKSB7XHJcbiAgICAgICAgICAgICAgY3VycmVudFNlY3Rpb24gPSBpbmRleDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBzZXRBY3RpdmVTZWN0aW9uKGN1cnJlbnRTZWN0aW9uKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVByb2dyZXNzLCB7IHBhc3NpdmU6IHRydWUgfSk7XHJcbiAgICAgIHVwZGF0ZVByb2dyZXNzKCk7XHJcblxyXG4gICAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVByb2dyZXNzKTtcclxuICAgIH1cclxuICB9LCBbc2VjdGlvbnNdKTtcclxuXHJcbiAgaWYgKHNlY3Rpb25zLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgcmV0dXJuIDxMdXh1cnlTY3JvbGxQcm9ncmVzcyBnb2xkQWNjZW50PXtnb2xkQWNjZW50fSBjbGFzc05hbWU9e2NsYXNzTmFtZX0gLz47XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPG1vdGlvbi5kaXZcclxuICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgdG9wLTAgbGVmdC0wIHJpZ2h0LTAgei00MCAke2NsYXNzTmFtZX1gfVxyXG4gICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IC00IH19XHJcbiAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogc2Nyb2xsUHJvZ3Jlc3MgPiAwLjA1ID8gMSA6IDAsIHk6IDAgfX1cclxuICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XHJcbiAgICA+XHJcbiAgICAgIHsvKiBNYWluIHByb2dyZXNzIGJhciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEgYmctc3RvbmUtbGlnaHQvMjAgYmFja2Ryb3AtYmx1ci1zbVwiPlxyXG4gICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgJHtcclxuICAgICAgICAgICAgZ29sZEFjY2VudCBcclxuICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tZW50ZXJwcmlzZS1icm93biB2aWEtdGVycmEgdG8tc2FuZCcgXHJcbiAgICAgICAgICAgICAgOiAnYmctY2hhcmNvYWwnXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgICAgIHN0eWxlPXt7IFxyXG4gICAgICAgICAgICBzY2FsZVg6IHNjcm9sbFByb2dyZXNzLFxyXG4gICAgICAgICAgICB0cmFuc2Zvcm1PcmlnaW46ICcwJSdcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjEgfX1cclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBTZWN0aW9uIGluZGljYXRvcnMgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLWZ1bGwgbGVmdC0wIHJpZ2h0LTAgZmxleCBqdXN0aWZ5LWNlbnRlciBtdC0yXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBweC00IHB5LTIgYmctc2FuY3R1YXJ5LzkwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1mdWxsIHNoYWRvdy1lbGVnYW50IGJvcmRlciBib3JkZXItZW50ZXJwcmlzZS1icm93bi8xMFwiPlxyXG4gICAgICAgICAge3NlY3Rpb25zLm1hcCgoc2VjdGlvbiwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cclxuICAgICAgICAgICAgICBrZXk9e3NlY3Rpb24uaWR9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0yIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgICAgICAgICBpbmRleCA9PT0gYWN0aXZlU2VjdGlvblxyXG4gICAgICAgICAgICAgICAgICA/IGdvbGRBY2NlbnQgXHJcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctZW50ZXJwcmlzZS1icm93biBzY2FsZS0xMjUnIFxyXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLWNoYXJjb2FsIHNjYWxlLTEyNSdcclxuICAgICAgICAgICAgICAgICAgOiAnYmctc3RvbmUtbGlnaHQgaG92ZXI6Ymctc3RvbmUnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZWxlbWVudCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHNlY3Rpb24uaWQpO1xyXG4gICAgICAgICAgICAgICAgaWYgKGVsZW1lbnQpIHtcclxuICAgICAgICAgICAgICAgICAgZWxlbWVudC5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMiB9fVxyXG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjkgfX1cclxuICAgICAgICAgICAgICB0aXRsZT17c2VjdGlvbi50aXRsZX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvbW90aW9uLmRpdj5cclxuICApO1xyXG59XHJcblxyXG4vLyBXYXJpYW50IG1pbmltYWxueVxyXG5leHBvcnQgZnVuY3Rpb24gTWluaW1hbFNjcm9sbFByb2dyZXNzKHsgZ29sZEFjY2VudCA9IHRydWUgfSkge1xyXG4gIGNvbnN0IFtzY3JvbGxQcm9ncmVzcywgc2V0U2Nyb2xsUHJvZ3Jlc3NdID0gdXNlU3RhdGUoMCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCB1cGRhdGVQcm9ncmVzcyA9ICgpID0+IHtcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgY29uc3Qgc2Nyb2xsZWQgPSB3aW5kb3cuc2Nyb2xsWTtcclxuICAgICAgICBjb25zdCBtYXhTY3JvbGwgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2Nyb2xsSGVpZ2h0IC0gd2luZG93LmlubmVySGVpZ2h0O1xyXG4gICAgICAgIGNvbnN0IHByb2dyZXNzID0gbWF4U2Nyb2xsID4gMCA/IHNjcm9sbGVkIC8gbWF4U2Nyb2xsIDogMDtcclxuICAgICAgICBzZXRTY3JvbGxQcm9ncmVzcyhwcm9ncmVzcyk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCB1cGRhdGVQcm9ncmVzcywgeyBwYXNzaXZlOiB0cnVlIH0pO1xyXG4gICAgICB1cGRhdGVQcm9ncmVzcygpO1xyXG5cclxuICAgICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCB1cGRhdGVQcm9ncmVzcyk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgcmlnaHQtMCBoLTAuNSB6LTQwXCI+XHJcbiAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgaC1mdWxsICR7XHJcbiAgICAgICAgICBnb2xkQWNjZW50IFxyXG4gICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tZW50ZXJwcmlzZS1icm93biB0by10ZXJyYScgXHJcbiAgICAgICAgICAgIDogJ2JnLWNoYXJjb2FsJ1xyXG4gICAgICAgIH1gfVxyXG4gICAgICAgIHN0eWxlPXt7IFxyXG4gICAgICAgICAgc2NhbGVYOiBzY3JvbGxQcm9ncmVzcyxcclxuICAgICAgICAgIHRyYW5zZm9ybU9yaWdpbjogJzAlJ1xyXG4gICAgICAgIH19XHJcbiAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4xIH19XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtb3Rpb24iLCJMdXh1cnlTY3JvbGxQcm9ncmVzcyIsImhlaWdodCIsInNob3dQZXJjZW50YWdlIiwiZ29sZEFjY2VudCIsImNsYXNzTmFtZSIsInNjcm9sbFByb2dyZXNzIiwic2V0U2Nyb2xsUHJvZ3Jlc3MiLCJpc1Zpc2libGUiLCJzZXRJc1Zpc2libGUiLCJ1cGRhdGVTY3JvbGxQcm9ncmVzcyIsInNjcm9sbGVkIiwid2luZG93Iiwic2Nyb2xsWSIsIm1heFNjcm9sbCIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50Iiwic2Nyb2xsSGVpZ2h0IiwiaW5uZXJIZWlnaHQiLCJwcm9ncmVzcyIsImFkZEV2ZW50TGlzdGVuZXIiLCJwYXNzaXZlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJlYXNlIiwic3R5bGUiLCJzY2FsZVgiLCJ0cmFuc2Zvcm1PcmlnaW4iLCJ3aWR0aCIsInNjYWxlIiwiZGVsYXkiLCJzcGFuIiwiTWF0aCIsInJvdW5kIiwiU2VjdGlvblNjcm9sbFByb2dyZXNzIiwic2VjdGlvbnMiLCJhY3RpdmVTZWN0aW9uIiwic2V0QWN0aXZlU2VjdGlvbiIsInVwZGF0ZVByb2dyZXNzIiwic2VjdGlvbkVsZW1lbnRzIiwibWFwIiwic2VjdGlvbiIsImdldEVsZW1lbnRCeUlkIiwiaWQiLCJmaWx0ZXIiLCJCb29sZWFuIiwiY3VycmVudFNlY3Rpb24iLCJmb3JFYWNoIiwiZWxlbWVudCIsImluZGV4IiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsInRvcCIsImxlbmd0aCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwid2hpbGVIb3ZlciIsIndoaWxlVGFwIiwidGl0bGUiLCJNaW5pbWFsU2Nyb2xsUHJvZ3Jlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LuxuryScrollProgress.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LuxuryWhatsApp.jsx":
/*!*******************************************!*\
  !*** ./src/components/LuxuryWhatsApp.jsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactWhatsApp: () => (/* binding */ CompactWhatsApp),\n/* harmony export */   \"default\": () => (/* binding */ LuxuryWhatsApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_LuxuryElements__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LuxuryElements */ \"(app-pages-browser)/./src/components/LuxuryElements.jsx\");\n/* __next_internal_client_entry_do_not_use__ default,CompactWhatsApp auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n/**\r\n * LuxuryWhatsApp - Luksusowy floating WhatsApp button\r\n * Z subtelną animacją i eleganckim tooltipem\r\n */ function LuxuryWhatsApp(param) {\n    let { phoneNumber = \"48123456789\", message = \"Cześć! Chciałabym dowiedzieć się więcej o retreatach BAKASANA 🧘‍♀️\", position = \"bottom-right\" } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LuxuryWhatsApp.useEffect\": ()=>{\n            // Pokaż przycisk po 3 sekundach\n            const timer = setTimeout({\n                \"LuxuryWhatsApp.useEffect.timer\": ()=>{\n                    setIsVisible(true);\n                }\n            }[\"LuxuryWhatsApp.useEffect.timer\"], 3000);\n            return ({\n                \"LuxuryWhatsApp.useEffect\": ()=>clearTimeout(timer)\n            })[\"LuxuryWhatsApp.useEffect\"];\n        }\n    }[\"LuxuryWhatsApp.useEffect\"], []);\n    const handleWhatsAppClick = ()=>{\n        const encodedMessage = encodeURIComponent(message);\n        const whatsappUrl = \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodedMessage);\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleToggleExpanded = ()=>{\n        setIsExpanded(!isExpanded);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-8 right-8 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.8,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.8,\n                        y: 20\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeOut\"\n                    },\n                    className: \"mb-4 bg-sanctuary rounded-2xl shadow-premium-shadow border border-enterprise-brown/10 p-6 max-w-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-enterprise-brown to-terra rounded-full flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-sanctuary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-cormorant text-lg text-charcoal font-medium\",\n                                                    children: \"BAKASANA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-sage\",\n                                                    children: \"Zazwyczaj odpowiadamy w ciągu godziny\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleToggleExpanded,\n                                    className: \"text-sage hover:text-charcoal transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-charcoal-light leading-relaxed\",\n                                children: \"Cześć! \\uD83D\\uDC4B Masz pytania o nasze retreaty? Napisz do nas - chętnie pomożemy!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                            onClick: handleWhatsAppClick,\n                            className: \"w-full bg-gradient-to-r from-enterprise-brown to-terra text-sanctuary py-3 px-4 rounded-xl font-inter font-medium text-sm transition-all duration-300 hover:shadow-lg hover:from-terra hover:to-sand\",\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            children: \"Napisz na WhatsApp\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-2 right-8 w-4 h-4 bg-sanctuary border-r border-b border-enterprise-brown/10 transform rotate-45\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_2__.LuxuryTooltip, {\n                content: \"Napisz do nas na WhatsApp\",\n                position: \"left\",\n                goldAccent: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        scale: 0,\n                        rotate: -180\n                    },\n                    animate: {\n                        scale: 1,\n                        rotate: 0\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 400,\n                        damping: 17,\n                        delay: 0.2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_2__.LuxuryFAB, {\n                        onClick: isExpanded ? handleToggleExpanded : handleWhatsAppClick,\n                        className: \"relative overflow-hidden group\",\n                        goldAccent: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute inset-0 rounded-full border-2 border-enterprise-brown/30\",\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ],\n                                    opacity: [\n                                        0.7,\n                                        0,\n                                        0.7\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                animate: {\n                                    rotate: isExpanded ? 45 : 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12\",\n                                initial: {\n                                    x: '-100%'\n                                },\n                                animate: {\n                                    x: '100%'\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    repeatDelay: 3,\n                                    ease: \"easeInOut\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-sanctuary\",\n                animate: {\n                    scale: [\n                        1,\n                        1.1,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(LuxuryWhatsApp, \"da1PdlF5OofnvxTwHS55vmciPkc=\");\n_c = LuxuryWhatsApp;\n// Wariant kompaktowy bez rozwijania\nfunction CompactWhatsApp(param) {\n    let { phoneNumber, message } = param;\n    _s1();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompactWhatsApp.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"CompactWhatsApp.useEffect.timer\": ()=>{\n                    setIsVisible(true);\n                }\n            }[\"CompactWhatsApp.useEffect.timer\"], 2000);\n            return ({\n                \"CompactWhatsApp.useEffect\": ()=>clearTimeout(timer)\n            })[\"CompactWhatsApp.useEffect\"];\n        }\n    }[\"CompactWhatsApp.useEffect\"], []);\n    const handleClick = ()=>{\n        const encodedMessage = encodeURIComponent(message || \"Cześć! Chciałabym dowiedzieć się więcej o BAKASANA 🧘‍♀️\");\n        const whatsappUrl = \"https://wa.me/\".concat(phoneNumber || \"48123456789\", \"?text=\").concat(encodedMessage);\n        window.open(whatsappUrl, '_blank');\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            scale: 0,\n            opacity: 0\n        },\n        animate: {\n            scale: 1,\n            opacity: 1\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 400,\n            damping: 17,\n            delay: 0.2\n        },\n        className: \"fixed bottom-8 right-8 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_2__.LuxuryFAB, {\n            onClick: handleClick,\n            className: \"relative group\",\n            goldAccent: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"absolute inset-0 rounded-full bg-enterprise-brown/20\",\n                    animate: {\n                        scale: [\n                            1,\n                            1.3,\n                            1\n                        ],\n                        opacity: [\n                            0.5,\n                            0,\n                            0.5\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\LuxuryWhatsApp.jsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s1(CompactWhatsApp, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c1 = CompactWhatsApp;\nvar _c, _c1;\n$RefreshReg$(_c, \"LuxuryWhatsApp\");\n$RefreshReg$(_c1, \"CompactWhatsApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LuxuryWhatsApp.jsx\n"));

/***/ })

});
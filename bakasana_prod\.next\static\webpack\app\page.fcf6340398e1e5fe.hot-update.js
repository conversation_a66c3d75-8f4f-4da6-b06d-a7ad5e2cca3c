"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvestmentSection.jsx":
/*!**********************************************!*\
  !*** ./src/components/InvestmentSection.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactInvestment: () => (/* binding */ CompactInvestment),\n/* harmony export */   \"default\": () => (/* binding */ InvestmentSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ScrollReveal */ \"(app-pages-browser)/./src/components/ScrollReveal.jsx\");\n/* harmony import */ var _components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/UnifiedTypography */ \"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx\");\n/* harmony import */ var _components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/UnifiedCard */ \"(app-pages-browser)/./src/components/ui/UnifiedCard.jsx\");\n/* harmony import */ var _components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/UnifiedButton */ \"(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\");\n/* harmony import */ var _data_investmentData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/investmentData */ \"(app-pages-browser)/./src/data/investmentData.js\");\n/* harmony import */ var _components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/IconSystem */ \"(app-pages-browser)/./src/components/ui/IconSystem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default,CompactInvestment auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\r\n * InvestmentSection - Sekcja \"Inwestycja w siebie\"\r\n * Pokazuje wartość, nie cenę - luksusowe podejście do prezentacji pakietów\r\n */ function InvestmentSection(param) {\n    let { title = \"Inwestycja w Twoją Transformację\", subtitle = \"Każdy pakiet to starannie przygotowane doświadczenie, które zmieni Twoje życie na zawsze\", showOnlyRetreats = false, variant = 'full' // 'full', 'compact', 'featured'\n     } = param;\n    _s();\n    const [selectedPackage, setSelectedPackage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const packages = showOnlyRetreats ? (0,_data_investmentData__WEBPACK_IMPORTED_MODULE_6__.getRetreatPackages)() : _data_investmentData__WEBPACK_IMPORTED_MODULE_6__.investmentPackages;\n    // Safety check for packages data\n    if (!packages || packages.length === 0) {\n        console.warn('InvestmentSection: No packages data available');\n        return null;\n    }\n    const formatPrice1 = (amount, currency, period)=>{\n        const formattedAmount = new Intl.NumberFormat('pl-PL').format(amount);\n        return period ? \"\".concat(formattedAmount, \" \").concat(currency, \"/\").concat(period) : \"\".concat(formattedAmount, \" \").concat(currency);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-section bg-sanctuary relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-[0.03]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C19B68' fill-opacity='0.1'%3E%3Cpath d='M50 50c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                        backgroundSize: '100px 100px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-container-sm relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                        variant: \"fadeInUp\",\n                        className: \"text-center mb-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.SectionTitle, {\n                                className: \"mb-md\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.LeadText, {\n                                className: \"text-sage max-w-3xl mx-auto\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__.StaggerContainer, {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-xl max-w-6xl mx-auto\",\n                        children: packages.map((pkg, index)=>{\n                            var _pkg_investment_installments, _pkg_investment_installments1;\n                            // Safety check for package structure\n                            if (!pkg || !pkg.id || !pkg.name) {\n                                console.warn('InvestmentSection: Invalid package data', pkg);\n                                return null;\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__.StaggerItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"elevated\",\n                                    padding: \"xl\",\n                                    className: \"h-full group relative overflow-hidden cursor-pointer\",\n                                    onClick: ()=>setSelectedPackage(selectedPackage === pkg.id ? null : pkg.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-enterprise-brown via-terra to-sand\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                    level: 3,\n                                                                    className: \"mb-2 group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                    children: pkg.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 82,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.BodyText, {\n                                                                    className: \"text-sage mb-sm\",\n                                                                    children: pkg.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-ash\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                            name: \"clock\",\n                                                                            size: \"xs\",\n                                                                            className: \"mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                            lineNumber: 89,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        pkg.duration,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                            name: \"location\",\n                                                                            size: \"xs\",\n                                                                            className: \"ml-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                            lineNumber: 91,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        pkg.location\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right ml-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-cormorant font-light text-charcoal mb-1\",\n                                                                    children: formatPrice1(pkg.investment.amount, pkg.investment.currency, pkg.investment.period)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                ((_pkg_investment_installments = pkg.investment.installments) === null || _pkg_investment_installments === void 0 ? void 0 : _pkg_investment_installments.available) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-sage\",\n                                                                    children: \"lub w ratach\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pkg.testimonial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-whisper p-4 rounded-lg border-l-4 border-enterprise-brown/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.BodyText, {\n                                                            className: \"text-sm italic text-charcoal-light mb-2\",\n                                                            children: [\n                                                                '\"',\n                                                                pkg.testimonial.text,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-sage font-medium\",\n                                                            children: [\n                                                                \"— \",\n                                                                pkg.testimonial.author\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-inter font-medium text-charcoal mb-sm text-sm uppercase tracking-wide\",\n                                                    children: \"Dlaczego to wyjątkowe\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                                    children: pkg.highlights.map((highlight, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-charcoal-light\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"check\",\n                                                                    size: \"xs\",\n                                                                    className: \"text-enterprise-brown mr-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                highlight\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"transition-all duration-500 overflow-hidden \".concat(selectedPackage === pkg.id ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-stone-light/30 pt-lg mb-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-inter font-medium text-charcoal mb-md text-sm uppercase tracking-wide\",\n                                                        children: \"Co jest wliczone w inwestycję\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-md\",\n                                                        children: pkg.included.map((category, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-cormorant text-lg text-enterprise-brown mb-sm\",\n                                                                        children: category.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                        lineNumber: 149,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: category.items.map((item, j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start text-sm text-charcoal-light\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                        name: \"dot\",\n                                                                                        size: \"xs\",\n                                                                                        className: \"text-terra mr-2 mt-1.5 flex-shrink-0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                                        lineNumber: 155,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    item\n                                                                                ]\n                                                                            }, j, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                                lineNumber: 154,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                        lineNumber: 152,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, i, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    pkg.notIncluded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-lg pt-md border-t border-stone-light/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-inter font-medium text-sage mb-sm text-sm\",\n                                                                children: \"Dodatkowo płatne:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1\",\n                                                                children: pkg.notIncluded.map((item, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-start text-sm text-sage\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                name: \"minus\",\n                                                                                size: \"xs\",\n                                                                                className: \"mr-2 mt-1.5 flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                                lineNumber: 173,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            item\n                                                                        ]\n                                                                    }, i, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ((_pkg_investment_installments1 = pkg.investment.installments) === null || _pkg_investment_installments1 === void 0 ? void 0 : _pkg_investment_installments1.available) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-lg p-md bg-linen rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-inter font-medium text-charcoal mb-sm text-sm\",\n                                                                children: \"Opcje płatności:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: pkg.investment.installments.options.map((option, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-charcoal-light\",\n                                                                                children: option.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-enterprise-brown\",\n                                                                                children: formatPrice1(option.amount, pkg.investment.currency)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                                lineNumber: 191,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, i, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 mt-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_5__.CTAButton, {\n                                                    className: \"flex-1\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                    // Logika rezerwacji\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                            name: \"heart\",\n                                                            size: \"sm\",\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Zarezerwuj miejsce\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_5__.SecondaryButton, {\n                                                    className: \"sm:w-auto\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedPackage(selectedPackage === pkg.id ? null : pkg.id);\n                                                    },\n                                                    children: [\n                                                        selectedPackage === pkg.id ? 'Zwiń szczegóły' : 'Zobacz szczegóły',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                            name: selectedPackage === pkg.id ? \"chevron-up\" : \"chevron-down\",\n                                                            size: \"sm\",\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            }, pkg.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                        variant: \"fadeInUp\",\n                        className: \"text-center mt-2xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.BodyText, {\n                                    className: \"text-sage mb-lg\",\n                                    children: \"Każdy pakiet został starannie przygotowany, aby zapewnić Ci pełne doświadczenie transformacji. Inwestujesz nie tylko w podr\\xf3ż, ale w nową wersję siebie.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-charcoal-light\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    name: \"shield\",\n                                                    size: \"sm\",\n                                                    className: \"text-enterprise-brown mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Gwarancja satysfakcji\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-charcoal-light\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    name: \"users\",\n                                                    size: \"sm\",\n                                                    className: \"text-enterprise-brown mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Małe, kameralne grupy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-charcoal-light\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    name: \"star\",\n                                                    size: \"sm\",\n                                                    className: \"text-enterprise-brown mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"7 lat doświadczenia\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(InvestmentSection, \"8wSAe2z5Hr/sDciTc2bjhx+hF0k=\");\n_c = InvestmentSection;\n// Wariant kompaktowy dla innych stron\nfunction CompactInvestment(param) {\n    let { maxPackages = 2 } = param;\n    const packages = (0,_data_investmentData__WEBPACK_IMPORTED_MODULE_6__.getRetreatPackages)().slice(0, maxPackages);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-xl bg-linen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-container-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                    variant: \"fadeInUp\",\n                    className: \"text-center mb-xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.SectionTitle, {\n                        className: \"mb-md\",\n                        children: \"Inwestuj w swoją transformację\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-lg max-w-4xl mx-auto\",\n                    children: packages.map((pkg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            variant: \"warm\",\n                            padding: \"lg\",\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    level: 4,\n                                    className: \"mb-sm\",\n                                    children: pkg.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_3__.BodyText, {\n                                    className: \"text-sage mb-md\",\n                                    children: pkg.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-cormorant font-light text-enterprise-brown mb-md\",\n                                    children: formatPrice(pkg.investment.amount, pkg.investment.currency)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_5__.CTAButton, {\n                                    size: \"sm\",\n                                    className: \"w-full\",\n                                    children: \"Dowiedz się więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, pkg.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\InvestmentSection.jsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CompactInvestment;\nvar _c, _c1;\n$RefreshReg$(_c, \"InvestmentSection\");\n$RefreshReg$(_c1, \"CompactInvestment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0ludmVzdG1lbnRTZWN0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ2dEO0FBQ1E7QUFDMUM7QUFDcUI7QUFDSTtBQUM3QjtBQUVsRDs7O0NBR0MsR0FFYyxTQUFTZSxrQkFBa0IsS0FLekM7UUFMeUMsRUFDeENDLFFBQVEsa0NBQWtDLEVBQzFDQyxXQUFXLDBGQUEwRixFQUNyR0MsbUJBQW1CLEtBQUssRUFDeEJDLFVBQVUsT0FBTyxnQ0FBZ0M7SUFBakMsRUFDakIsR0FMeUM7O0lBTXhDLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU1xQixXQUFXSixtQkFBbUJMLHdFQUFrQkEsS0FBS0Qsb0VBQWtCQTtJQUU3RSxpQ0FBaUM7SUFDakMsSUFBSSxDQUFDVSxZQUFZQSxTQUFTQyxNQUFNLEtBQUssR0FBRztRQUN0Q0MsUUFBUUMsSUFBSSxDQUFDO1FBQ2IsT0FBTztJQUNUO0lBRUEsTUFBTUMsZUFBYyxDQUFDQyxRQUFRQyxVQUFVQztRQUNyQyxNQUFNQyxrQkFBa0IsSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVNDLE1BQU0sQ0FBQ047UUFDOUQsT0FBT0UsU0FBUyxHQUFzQkQsT0FBbkJFLGlCQUFnQixLQUFlRCxPQUFaRCxVQUFTLEtBQVUsT0FBUEMsVUFBVyxHQUFzQkQsT0FBbkJFLGlCQUFnQixLQUFZLE9BQVRGO0lBQ3JGO0lBRUEscUJBQ0UsOERBQUNNO1FBQVFDLFdBQVU7OzBCQUVqQiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUNDRCxXQUFVO29CQUNWRSxPQUFPO3dCQUNMQyxpQkFBa0I7d0JBQ2xCQyxnQkFBZ0I7b0JBQ2xCOzs7Ozs7Ozs7OzswQkFJSiw4REFBQ0g7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDakMsa0VBQVlBO3dCQUFDaUIsU0FBUTt3QkFBV2dCLFdBQVU7OzBDQUN6Qyw4REFBQzlCLDBFQUFZQTtnQ0FBQzhCLFdBQVU7MENBQ3JCbkI7Ozs7OzswQ0FFSCw4REFBQ1Qsc0VBQVFBO2dDQUFDNEIsV0FBVTswQ0FDakJsQjs7Ozs7Ozs7Ozs7O2tDQUlMLDhEQUFDZCxzRUFBZ0JBO3dCQUFDZ0MsV0FBVTtrQ0FDekJiLFNBQVNrQixHQUFHLENBQUMsQ0FBQ0MsS0FBS0M7Z0NBeUNQRCw4QkFpRkZBOzRCQXpIVCxxQ0FBcUM7NEJBQ3JDLElBQUksQ0FBQ0EsT0FBTyxDQUFDQSxJQUFJRSxFQUFFLElBQUksQ0FBQ0YsSUFBSUcsSUFBSSxFQUFFO2dDQUNoQ3BCLFFBQVFDLElBQUksQ0FBQywyQ0FBMkNnQjtnQ0FDeEQsT0FBTzs0QkFDVDs0QkFFQSxxQkFDQSw4REFBQ3JDLGlFQUFXQTswQ0FDViw0RUFBQ0ssa0VBQVdBO29DQUNWVSxTQUFRO29DQUNSMEIsU0FBUTtvQ0FDUlYsV0FBVTtvQ0FDVlcsU0FBUyxJQUFNekIsbUJBQW1CRCxvQkFBb0JxQixJQUFJRSxFQUFFLEdBQUcsT0FBT0YsSUFBSUUsRUFBRTs7c0RBRzVFLDhEQUFDUDs0Q0FBSUQsV0FBVTs7Ozs7O3NEQUdmLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDM0IsdUVBQVNBO29FQUFDdUMsT0FBTztvRUFBR1osV0FBVTs4RUFDNUJNLElBQUlHLElBQUk7Ozs7Ozs4RUFFWCw4REFBQ3RDLHNFQUFRQTtvRUFBQzZCLFdBQVU7OEVBQ2pCTSxJQUFJeEIsUUFBUTs7Ozs7OzhFQUVmLDhEQUFDbUI7b0VBQUlELFdBQVU7O3NGQUNiLDhEQUFDckIsMkRBQUlBOzRFQUFDOEIsTUFBSzs0RUFBUUksTUFBSzs0RUFBS2IsV0FBVTs7Ozs7O3dFQUN0Q00sSUFBSVEsUUFBUTtzRkFDYiw4REFBQ25DLDJEQUFJQTs0RUFBQzhCLE1BQUs7NEVBQVdJLE1BQUs7NEVBQUtiLFdBQVU7Ozs7Ozt3RUFDekNNLElBQUlTLFFBQVE7Ozs7Ozs7Ozs7Ozs7c0VBS2pCLDhEQUFDZDs0REFBSUQsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFJRCxXQUFVOzhFQUNaVCxhQUFZZSxJQUFJVSxVQUFVLENBQUN4QixNQUFNLEVBQUVjLElBQUlVLFVBQVUsQ0FBQ3ZCLFFBQVEsRUFBRWEsSUFBSVUsVUFBVSxDQUFDdEIsTUFBTTs7Ozs7O2dFQUVuRlksRUFBQUEsK0JBQUFBLElBQUlVLFVBQVUsQ0FBQ0MsWUFBWSxjQUEzQlgsbURBQUFBLDZCQUE2QlksU0FBUyxtQkFDckMsOERBQUNqQjtvRUFBSUQsV0FBVTs4RUFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztnREFReENNLElBQUlhLFdBQVcsa0JBQ2QsOERBQUNsQjtvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUM3QixzRUFBUUE7NERBQUM2QixXQUFVOztnRUFBMEM7Z0VBQzFETSxJQUFJYSxXQUFXLENBQUNDLElBQUk7Z0VBQUM7Ozs7Ozs7c0VBRXpCLDhEQUFDbkI7NERBQUlELFdBQVU7O2dFQUFnQztnRUFDMUNNLElBQUlhLFdBQVcsQ0FBQ0UsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFPakMsOERBQUNwQjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNzQjtvREFBR3RCLFdBQVU7OERBQTZFOzs7Ozs7OERBRzNGLDhEQUFDQztvREFBSUQsV0FBVTs4REFDWk0sSUFBSWlCLFVBQVUsQ0FBQ2xCLEdBQUcsQ0FBQyxDQUFDbUIsV0FBV0Msa0JBQzlCLDhEQUFDeEI7NERBQVlELFdBQVU7OzhFQUNyQiw4REFBQ3JCLDJEQUFJQTtvRUFBQzhCLE1BQUs7b0VBQVFJLE1BQUs7b0VBQUtiLFdBQVU7Ozs7OztnRUFDdEN3Qjs7MkRBRk9DOzs7Ozs7Ozs7Ozs7Ozs7O3NEQVNoQiw4REFBQ3hCOzRDQUFJRCxXQUFXLCtDQUVmLE9BRENmLG9CQUFvQnFCLElBQUlFLEVBQUUsR0FBRywrQkFBK0I7c0RBRTVELDRFQUFDUDtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNzQjt3REFBR3RCLFdBQVU7a0VBQTZFOzs7Ozs7a0VBSTNGLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFDWk0sSUFBSW9CLFFBQVEsQ0FBQ3JCLEdBQUcsQ0FBQyxDQUFDc0IsVUFBVUYsa0JBQzNCLDhEQUFDeEI7O2tGQUNDLDhEQUFDMkI7d0VBQUc1QixXQUFVO2tGQUNYMkIsU0FBU0EsUUFBUTs7Ozs7O2tGQUVwQiw4REFBQ0U7d0VBQUc3QixXQUFVO2tGQUNYMkIsU0FBU0csS0FBSyxDQUFDekIsR0FBRyxDQUFDLENBQUMwQixNQUFNQyxrQkFDekIsOERBQUNDO2dGQUFXakMsV0FBVTs7a0dBQ3BCLDhEQUFDckIsMkRBQUlBO3dGQUFDOEIsTUFBSzt3RkFBTUksTUFBSzt3RkFBS2IsV0FBVTs7Ozs7O29GQUNwQytCOzsrRUFGTUM7Ozs7Ozs7Ozs7OytEQU5MUDs7Ozs7Ozs7OztvREFpQmJuQixJQUFJNEIsV0FBVyxrQkFDZCw4REFBQ2pDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQzRCO2dFQUFHNUIsV0FBVTswRUFBaUQ7Ozs7OzswRUFHL0QsOERBQUM2QjtnRUFBRzdCLFdBQVU7MEVBQ1hNLElBQUk0QixXQUFXLENBQUM3QixHQUFHLENBQUMsQ0FBQzBCLE1BQU1OLGtCQUMxQiw4REFBQ1E7d0VBQVdqQyxXQUFVOzswRkFDcEIsOERBQUNyQiwyREFBSUE7Z0ZBQUM4QixNQUFLO2dGQUFRSSxNQUFLO2dGQUFLYixXQUFVOzs7Ozs7NEVBQ3RDK0I7O3VFQUZNTjs7Ozs7Ozs7Ozs7Ozs7OztvREFVaEJuQixFQUFBQSxnQ0FBQUEsSUFBSVUsVUFBVSxDQUFDQyxZQUFZLGNBQTNCWCxvREFBQUEsOEJBQTZCWSxTQUFTLG1CQUNyQyw4REFBQ2pCO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQzRCO2dFQUFHNUIsV0FBVTswRUFBcUQ7Ozs7OzswRUFHbkUsOERBQUNDO2dFQUFJRCxXQUFVOzBFQUNaTSxJQUFJVSxVQUFVLENBQUNDLFlBQVksQ0FBQ2tCLE9BQU8sQ0FBQzlCLEdBQUcsQ0FBQyxDQUFDK0IsUUFBUVgsa0JBQ2hELDhEQUFDeEI7d0VBQVlELFdBQVU7OzBGQUNyQiw4REFBQ3FDO2dGQUFLckMsV0FBVTswRkFBdUJvQyxPQUFPRSxXQUFXOzs7Ozs7MEZBQ3pELDhEQUFDRDtnRkFBS3JDLFdBQVU7MEZBQ2JULGFBQVk2QyxPQUFPNUMsTUFBTSxFQUFFYyxJQUFJVSxVQUFVLENBQUN2QixRQUFROzs7Ozs7O3VFQUg3Q2dDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBY3RCLDhEQUFDeEI7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDekIsbUVBQVNBO29EQUNSeUIsV0FBVTtvREFDVlcsU0FBUyxDQUFDNEI7d0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCLG9CQUFvQjtvREFDdEI7O3NFQUVBLDhEQUFDN0QsMkRBQUlBOzREQUFDOEIsTUFBSzs0REFBUUksTUFBSzs0REFBS2IsV0FBVTs7Ozs7O3dEQUFTOzs7Ozs7OzhEQUdsRCw4REFBQ3hCLHlFQUFlQTtvREFDZHdCLFdBQVU7b0RBQ1ZXLFNBQVMsQ0FBQzRCO3dEQUNSQSxFQUFFQyxlQUFlO3dEQUNqQnRELG1CQUFtQkQsb0JBQW9CcUIsSUFBSUUsRUFBRSxHQUFHLE9BQU9GLElBQUlFLEVBQUU7b0RBQy9EOzt3REFFQ3ZCLG9CQUFvQnFCLElBQUlFLEVBQUUsR0FBRyxtQkFBbUI7c0VBQ2pELDhEQUFDN0IsMkRBQUlBOzREQUNIOEIsTUFBTXhCLG9CQUFvQnFCLElBQUlFLEVBQUUsR0FBRyxlQUFlOzREQUNsREssTUFBSzs0REFDTGIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQTdKRk0sSUFBSUUsRUFBRTs7Ozs7d0JBb0sxQjs7Ozs7O2tDQUlGLDhEQUFDekMsa0VBQVlBO3dCQUFDaUIsU0FBUTt3QkFBV2dCLFdBQVU7a0NBQ3pDLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUM3QixzRUFBUUE7b0NBQUM2QixXQUFVOzhDQUFrQjs7Ozs7OzhDQUt0Qyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNyQiwyREFBSUE7b0RBQUM4QixNQUFLO29EQUFTSSxNQUFLO29EQUFLYixXQUFVOzs7Ozs7Z0RBQStCOzs7Ozs7O3NEQUd6RSw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDckIsMkRBQUlBO29EQUFDOEIsTUFBSztvREFBUUksTUFBSztvREFBS2IsV0FBVTs7Ozs7O2dEQUErQjs7Ozs7OztzREFHeEUsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ3JCLDJEQUFJQTtvREFBQzhCLE1BQUs7b0RBQU9JLE1BQUs7b0RBQUtiLFdBQVU7Ozs7OztnREFBK0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNyRjtHQXRQd0JwQjtLQUFBQTtBQXdQeEIsc0NBQXNDO0FBQy9CLFNBQVM2RCxrQkFBa0IsS0FBbUI7UUFBbkIsRUFBRUMsY0FBYyxDQUFDLEVBQUUsR0FBbkI7SUFDaEMsTUFBTXZELFdBQVdULHdFQUFrQkEsR0FBR2lFLEtBQUssQ0FBQyxHQUFHRDtJQUUvQyxxQkFDRSw4REFBQzNDO1FBQVFDLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ2pDLGtFQUFZQTtvQkFBQ2lCLFNBQVE7b0JBQVdnQixXQUFVOzhCQUN6Qyw0RUFBQzlCLDBFQUFZQTt3QkFBQzhCLFdBQVU7a0NBQVE7Ozs7Ozs7Ozs7OzhCQUtsQyw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1piLFNBQVNrQixHQUFHLENBQUMsQ0FBQ0Msb0JBQ2IsOERBQUNoQyxrRUFBV0E7NEJBQWNVLFNBQVE7NEJBQU8wQixTQUFROzRCQUFLVixXQUFVOzs4Q0FDOUQsOERBQUMzQix1RUFBU0E7b0NBQUN1QyxPQUFPO29DQUFHWixXQUFVOzhDQUM1Qk0sSUFBSUcsSUFBSTs7Ozs7OzhDQUVYLDhEQUFDdEMsc0VBQVFBO29DQUFDNkIsV0FBVTs4Q0FDakJNLElBQUl4QixRQUFROzs7Ozs7OENBRWYsOERBQUNtQjtvQ0FBSUQsV0FBVTs4Q0FDWlQsWUFBWWUsSUFBSVUsVUFBVSxDQUFDeEIsTUFBTSxFQUFFYyxJQUFJVSxVQUFVLENBQUN2QixRQUFROzs7Ozs7OENBRTdELDhEQUFDbEIsbUVBQVNBO29DQUFDc0MsTUFBSztvQ0FBS2IsV0FBVTs4Q0FBUzs7Ozs7OzsyQkFWeEJNLElBQUlFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1CcEM7TUFqQ2dCaUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxzcmNcXGNvbXBvbmVudHNcXEludmVzdG1lbnRTZWN0aW9uLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFNjcm9sbFJldmVhbCwgU3RhZ2dlckNvbnRhaW5lciwgU3RhZ2dlckl0ZW0gfSBmcm9tICdAL2NvbXBvbmVudHMvU2Nyb2xsUmV2ZWFsJztcclxuaW1wb3J0IHsgU2VjdGlvblRpdGxlLCBCb2R5VGV4dCwgTGVhZFRleHQsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9VbmlmaWVkVHlwb2dyYXBoeSc7XHJcbmltcG9ydCBVbmlmaWVkQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvdWkvVW5pZmllZENhcmQnO1xyXG5pbXBvcnQgeyBDVEFCdXR0b24sIFNlY29uZGFyeUJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9VbmlmaWVkQnV0dG9uJztcclxuaW1wb3J0IHsgaW52ZXN0bWVudFBhY2thZ2VzLCBnZXRSZXRyZWF0UGFja2FnZXMgfSBmcm9tICdAL2RhdGEvaW52ZXN0bWVudERhdGEnO1xyXG5pbXBvcnQgeyBJY29uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0ljb25TeXN0ZW0nO1xyXG5cclxuLyoqXHJcbiAqIEludmVzdG1lbnRTZWN0aW9uIC0gU2VrY2phIFwiSW53ZXN0eWNqYSB3IHNpZWJpZVwiXHJcbiAqIFBva2F6dWplIHdhcnRvxZvEhywgbmllIGNlbsSZIC0gbHVrc3Vzb3dlIHBvZGVqxZtjaWUgZG8gcHJlemVudGFjamkgcGFraWV0w7N3XHJcbiAqL1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSW52ZXN0bWVudFNlY3Rpb24oeyBcclxuICB0aXRsZSA9IFwiSW53ZXN0eWNqYSB3IFR3b2rEhSBUcmFuc2Zvcm1hY2rEmVwiLFxyXG4gIHN1YnRpdGxlID0gXCJLYcW8ZHkgcGFraWV0IHRvIHN0YXJhbm5pZSBwcnp5Z290b3dhbmUgZG/Fm3dpYWRjemVuaWUsIGt0w7NyZSB6bWllbmkgVHdvamUgxbx5Y2llIG5hIHphd3N6ZVwiLFxyXG4gIHNob3dPbmx5UmV0cmVhdHMgPSBmYWxzZSxcclxuICB2YXJpYW50ID0gJ2Z1bGwnIC8vICdmdWxsJywgJ2NvbXBhY3QnLCAnZmVhdHVyZWQnXHJcbn0pIHtcclxuICBjb25zdCBbc2VsZWN0ZWRQYWNrYWdlLCBzZXRTZWxlY3RlZFBhY2thZ2VdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgcGFja2FnZXMgPSBzaG93T25seVJldHJlYXRzID8gZ2V0UmV0cmVhdFBhY2thZ2VzKCkgOiBpbnZlc3RtZW50UGFja2FnZXM7XHJcblxyXG4gIC8vIFNhZmV0eSBjaGVjayBmb3IgcGFja2FnZXMgZGF0YVxyXG4gIGlmICghcGFja2FnZXMgfHwgcGFja2FnZXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICBjb25zb2xlLndhcm4oJ0ludmVzdG1lbnRTZWN0aW9uOiBObyBwYWNrYWdlcyBkYXRhIGF2YWlsYWJsZScpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG5cclxuICBjb25zdCBmb3JtYXRQcmljZSA9IChhbW91bnQsIGN1cnJlbmN5LCBwZXJpb2QpID0+IHtcclxuICAgIGNvbnN0IGZvcm1hdHRlZEFtb3VudCA9IG5ldyBJbnRsLk51bWJlckZvcm1hdCgncGwtUEwnKS5mb3JtYXQoYW1vdW50KTtcclxuICAgIHJldHVybiBwZXJpb2QgPyBgJHtmb3JtYXR0ZWRBbW91bnR9ICR7Y3VycmVuY3l9LyR7cGVyaW9kfWAgOiBgJHtmb3JtYXR0ZWRBbW91bnR9ICR7Y3VycmVuY3l9YDtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktc2VjdGlvbiBiZy1zYW5jdHVhcnkgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgIHsvKiBMdWtzdXNvd3kgd3rDs3IgdyB0bGUgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LVswLjAzXVwiPlxyXG4gICAgICAgIDxkaXYgXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCJcclxuICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0Nzdmcgd2lkdGg9JzEwMCcgaGVpZ2h0PScxMDAnIHZpZXdCb3g9JzAgMCAxMDAgMTAwJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNFJTNDZyBmaWxsPSdub25lJyBmaWxsLXJ1bGU9J2V2ZW5vZGQnJTNFJTNDZyBmaWxsPSclMjNDMTlCNjgnIGZpbGwtb3BhY2l0eT0nMC4xJyUzRSUzQ3BhdGggZD0nTTUwIDUwYzAtNS41LTQuNS0xMC0xMC0xMHMtMTAgNC41LTEwIDEwIDQuNSAxMCAxMCAxMCAxMC00LjUgMTAtMTB6bTEwIDBjMC01LjUtNC41LTEwLTEwLTEwcy0xMCA0LjUtMTAgMTAgNC41IDEwIDEwIDEwIDEwLTQuNSAxMC0xMHonLyUzRSUzQy9nJTNFJTNDL2clM0UlM0Mvc3ZnJTNFXCIpYCxcclxuICAgICAgICAgICAgYmFja2dyb3VuZFNpemU6ICcxMDBweCAxMDBweCdcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LWNvbnRhaW5lci1zbSByZWxhdGl2ZVwiPlxyXG4gICAgICAgIDxTY3JvbGxSZXZlYWwgdmFyaWFudD1cImZhZGVJblVwXCIgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMnhsXCI+XHJcbiAgICAgICAgICA8U2VjdGlvblRpdGxlIGNsYXNzTmFtZT1cIm1iLW1kXCI+XHJcbiAgICAgICAgICAgIHt0aXRsZX1cclxuICAgICAgICAgIDwvU2VjdGlvblRpdGxlPlxyXG4gICAgICAgICAgPExlYWRUZXh0IGNsYXNzTmFtZT1cInRleHQtc2FnZSBtYXgtdy0zeGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAgICB7c3VidGl0bGV9XHJcbiAgICAgICAgICA8L0xlYWRUZXh0PlxyXG4gICAgICAgIDwvU2Nyb2xsUmV2ZWFsPlxyXG5cclxuICAgICAgICA8U3RhZ2dlckNvbnRhaW5lciBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC14bCBtYXgtdy02eGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAge3BhY2thZ2VzLm1hcCgocGtnLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAvLyBTYWZldHkgY2hlY2sgZm9yIHBhY2thZ2Ugc3RydWN0dXJlXHJcbiAgICAgICAgICAgIGlmICghcGtnIHx8ICFwa2cuaWQgfHwgIXBrZy5uYW1lKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdJbnZlc3RtZW50U2VjdGlvbjogSW52YWxpZCBwYWNrYWdlIGRhdGEnLCBwa2cpO1xyXG4gICAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8U3RhZ2dlckl0ZW0ga2V5PXtwa2cuaWR9PlxyXG4gICAgICAgICAgICAgIDxVbmlmaWVkQ2FyZCBcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJlbGV2YXRlZFwiIFxyXG4gICAgICAgICAgICAgICAgcGFkZGluZz1cInhsXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtZnVsbCBncm91cCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRQYWNrYWdlKHNlbGVjdGVkUGFja2FnZSA9PT0gcGtnLmlkID8gbnVsbCA6IHBrZy5pZCl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgey8qIEx1a3N1c293eSBha2NlbnQgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCByaWdodC0wIGgtMiBiZy1ncmFkaWVudC10by1yIGZyb20tZW50ZXJwcmlzZS1icm93biB2aWEtdGVycmEgdG8tc2FuZFwiIC8+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHsvKiBIZWFkZXIgcGFraWV0dSAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItbGdcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi1tZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGxldmVsPXszfSBjbGFzc05hbWU9XCJtYi0yIGdyb3VwLWhvdmVyOnRleHQtZW50ZXJwcmlzZS1icm93biB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3BrZy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Qm9keVRleHQgY2xhc3NOYW1lPVwidGV4dC1zYWdlIG1iLXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtwa2cuc3VidGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0JvZHlUZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtIHRleHQtYXNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIG5hbWU9XCJjbG9ja1wiIHNpemU9XCJ4c1wiIGNsYXNzTmFtZT1cIm1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cGtnLmR1cmF0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBuYW1lPVwibG9jYXRpb25cIiBzaXplPVwieHNcIiBjbGFzc05hbWU9XCJtbC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cGtnLmxvY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIENlbmEgeiBsdWtzdXNvd3ltIHN0eWxlbSAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHQgbWwtNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWNvcm1vcmFudCBmb250LWxpZ2h0IHRleHQtY2hhcmNvYWwgbWItMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2UocGtnLmludmVzdG1lbnQuYW1vdW50LCBwa2cuaW52ZXN0bWVudC5jdXJyZW5jeSwgcGtnLmludmVzdG1lbnQucGVyaW9kKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAge3BrZy5pbnZlc3RtZW50Lmluc3RhbGxtZW50cz8uYXZhaWxhYmxlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2FnZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGx1YiB3IHJhdGFjaFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIFRlc3RpbW9uaWFsICovfVxyXG4gICAgICAgICAgICAgICAgICB7cGtnLnRlc3RpbW9uaWFsICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXNwZXIgcC00IHJvdW5kZWQtbGcgYm9yZGVyLWwtNCBib3JkZXItZW50ZXJwcmlzZS1icm93bi8yMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJvZHlUZXh0IGNsYXNzTmFtZT1cInRleHQtc20gaXRhbGljIHRleHQtY2hhcmNvYWwtbGlnaHQgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcIntwa2cudGVzdGltb25pYWwudGV4dH1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Cb2R5VGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNhZ2UgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAg4oCUIHtwa2cudGVzdGltb25pYWwuYXV0aG9yfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogSGlnaGxpZ2h0cyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItbGdcIj5cclxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtaW50ZXIgZm9udC1tZWRpdW0gdGV4dC1jaGFyY29hbCBtYi1zbSB0ZXh0LXNtIHVwcGVyY2FzZSB0cmFja2luZy13aWRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgRGxhY3plZ28gdG8gd3lqxIV0a293ZVxyXG4gICAgICAgICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICB7cGtnLmhpZ2hsaWdodHMubWFwKChoaWdobGlnaHQsIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtIHRleHQtY2hhcmNvYWwtbGlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gbmFtZT1cImNoZWNrXCIgc2l6ZT1cInhzXCIgY2xhc3NOYW1lPVwidGV4dC1lbnRlcnByaXNlLWJyb3duIG1yLTIgZmxleC1zaHJpbmstMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtoaWdobGlnaHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogUm96d2lqYW5lIHN6Y3plZ8OzxYJ5ICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgb3ZlcmZsb3ctaGlkZGVuICR7XHJcbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUGFja2FnZSA9PT0gcGtnLmlkID8gJ21heC1oLVsyMDAwcHhdIG9wYWNpdHktMTAwJyA6ICdtYXgtaC0wIG9wYWNpdHktMCdcclxuICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItc3RvbmUtbGlnaHQvMzAgcHQtbGcgbWItbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1pbnRlciBmb250LW1lZGl1bSB0ZXh0LWNoYXJjb2FsIG1iLW1kIHRleHQtc20gdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIENvIGplc3Qgd2xpY3pvbmUgdyBpbndlc3R5Y2rEmVxyXG4gICAgICAgICAgICAgICAgICAgIDwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7cGtnLmluY2x1ZGVkLm1hcCgoY2F0ZWdvcnksIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LWNvcm1vcmFudCB0ZXh0LWxnIHRleHQtZW50ZXJwcmlzZS1icm93biBtYi1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5LmNhdGVnb3J5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Lml0ZW1zLm1hcCgoaXRlbSwgaikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtqfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHRleHQtc20gdGV4dC1jaGFyY29hbC1saWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIG5hbWU9XCJkb3RcIiBzaXplPVwieHNcIiBjbGFzc05hbWU9XCJ0ZXh0LXRlcnJhIG1yLTIgbXQtMS41IGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIE5pZSB3bGljem9uZSAqL31cclxuICAgICAgICAgICAgICAgICAgICB7cGtnLm5vdEluY2x1ZGVkICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtbGcgcHQtbWQgYm9yZGVyLXQgYm9yZGVyLXN0b25lLWxpZ2h0LzIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LWludGVyIGZvbnQtbWVkaXVtIHRleHQtc2FnZSBtYi1zbSB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgRG9kYXRrb3dvIHDFgmF0bmU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cGtnLm5vdEluY2x1ZGVkLm1hcCgoaXRlbSwgaSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCB0ZXh0LXNtIHRleHQtc2FnZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBuYW1lPVwibWludXNcIiBzaXplPVwieHNcIiBjbGFzc05hbWU9XCJtci0yIG10LTEuNSBmbGV4LXNocmluay0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIE9wY2plIHDFgmF0bm/Fm2NpICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHtwa2cuaW52ZXN0bWVudC5pbnN0YWxsbWVudHM/LmF2YWlsYWJsZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LWxnIHAtbWQgYmctbGluZW4gcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1pbnRlciBmb250LW1lZGl1bSB0ZXh0LWNoYXJjb2FsIG1iLXNtIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBPcGNqZSBwxYJhdG5vxZtjaTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9oNT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cGtnLmludmVzdG1lbnQuaW5zdGFsbG1lbnRzLm9wdGlvbnMubWFwKChvcHRpb24sIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWNoYXJjb2FsLWxpZ2h0XCI+e29wdGlvbi5kZXNjcmlwdGlvbn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZW50ZXJwcmlzZS1icm93blwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShvcHRpb24uYW1vdW50LCBwa2cuaW52ZXN0bWVudC5jdXJyZW5jeSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFByenljaXNraSBha2NqaSAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBtdC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICA8Q1RBQnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyBMb2dpa2EgcmV6ZXJ3YWNqaVxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8SWNvbiBuYW1lPVwiaGVhcnRcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICBaYXJlemVyd3VqIG1pZWpzY2VcclxuICAgICAgICAgICAgICAgICAgPC9DVEFCdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWNvbmRhcnlCdXR0b24gXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic206dy1hdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkUGFja2FnZShzZWxlY3RlZFBhY2thZ2UgPT09IHBrZy5pZCA/IG51bGwgOiBwa2cuaWQpO1xyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQYWNrYWdlID09PSBwa2cuaWQgPyAnWndpxYQgc3pjemVnw7PFgnknIDogJ1pvYmFjeiBzemN6ZWfDs8WCeSd9XHJcbiAgICAgICAgICAgICAgICAgICAgPEljb24gXHJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPXtzZWxlY3RlZFBhY2thZ2UgPT09IHBrZy5pZCA/IFwiY2hldnJvbi11cFwiIDogXCJjaGV2cm9uLWRvd25cIn0gXHJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIiBcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTJcIiBcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L1NlY29uZGFyeUJ1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvVW5pZmllZENhcmQ+XHJcbiAgICAgICAgICAgIDwvU3RhZ2dlckl0ZW0+XHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICB9KX1cclxuICAgICAgICA8L1N0YWdnZXJDb250YWluZXI+XHJcblxyXG4gICAgICAgIHsvKiBEb2RhdGtvd2UgaW5mb3JtYWNqZSAqL31cclxuICAgICAgICA8U2Nyb2xsUmV2ZWFsIHZhcmlhbnQ9XCJmYWRlSW5VcFwiIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG10LTJ4bFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0zeGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAgICA8Qm9keVRleHQgY2xhc3NOYW1lPVwidGV4dC1zYWdlIG1iLWxnXCI+XHJcbiAgICAgICAgICAgICAgS2HFvGR5IHBha2lldCB6b3N0YcWCIHN0YXJhbm5pZSBwcnp5Z290b3dhbnksIGFieSB6YXBld25pxIcgQ2kgcGXFgm5lIGRvxZt3aWFkY3plbmllIHRyYW5zZm9ybWFjamkuIFxyXG4gICAgICAgICAgICAgIElud2VzdHVqZXN6IG5pZSB0eWxrbyB3IHBvZHLDs8W8LCBhbGUgdyBub3fEhSB3ZXJzasSZIHNpZWJpZS5cclxuICAgICAgICAgICAgPC9Cb2R5VGV4dD5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtc20gdGV4dC1jaGFyY29hbC1saWdodFwiPlxyXG4gICAgICAgICAgICAgICAgPEljb24gbmFtZT1cInNoaWVsZFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtZW50ZXJwcmlzZS1icm93biBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgIEd3YXJhbmNqYSBzYXR5c2Zha2NqaVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LWNoYXJjb2FsLWxpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbiBuYW1lPVwidXNlcnNcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LWVudGVycHJpc2UtYnJvd24gbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICBNYcWCZSwga2FtZXJhbG5lIGdydXB5XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtIHRleHQtY2hhcmNvYWwtbGlnaHRcIj5cclxuICAgICAgICAgICAgICAgIDxJY29uIG5hbWU9XCJzdGFyXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1lbnRlcnByaXNlLWJyb3duIG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgNyBsYXQgZG/Fm3dpYWRjemVuaWFcclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1Njcm9sbFJldmVhbD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L3NlY3Rpb24+XHJcbiAgKTtcclxufVxyXG5cclxuLy8gV2FyaWFudCBrb21wYWt0b3d5IGRsYSBpbm55Y2ggc3Ryb25cclxuZXhwb3J0IGZ1bmN0aW9uIENvbXBhY3RJbnZlc3RtZW50KHsgbWF4UGFja2FnZXMgPSAyIH0pIHtcclxuICBjb25zdCBwYWNrYWdlcyA9IGdldFJldHJlYXRQYWNrYWdlcygpLnNsaWNlKDAsIG1heFBhY2thZ2VzKTtcclxuICBcclxuICByZXR1cm4gKFxyXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHkteGwgYmctbGluZW5cIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC1jb250YWluZXItc21cIj5cclxuICAgICAgICA8U2Nyb2xsUmV2ZWFsIHZhcmlhbnQ9XCJmYWRlSW5VcFwiIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLXhsXCI+XHJcbiAgICAgICAgICA8U2VjdGlvblRpdGxlIGNsYXNzTmFtZT1cIm1iLW1kXCI+XHJcbiAgICAgICAgICAgIElud2VzdHVqIHcgc3dvasSFIHRyYW5zZm9ybWFjasSZXHJcbiAgICAgICAgICA8L1NlY3Rpb25UaXRsZT5cclxuICAgICAgICA8L1Njcm9sbFJldmVhbD5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC1sZyBtYXgtdy00eGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAge3BhY2thZ2VzLm1hcCgocGtnKSA9PiAoXHJcbiAgICAgICAgICAgIDxVbmlmaWVkQ2FyZCBrZXk9e3BrZy5pZH0gdmFyaWFudD1cIndhcm1cIiBwYWRkaW5nPVwibGdcIiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgbGV2ZWw9ezR9IGNsYXNzTmFtZT1cIm1iLXNtXCI+XHJcbiAgICAgICAgICAgICAgICB7cGtnLm5hbWV9XHJcbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgPEJvZHlUZXh0IGNsYXNzTmFtZT1cInRleHQtc2FnZSBtYi1tZFwiPlxyXG4gICAgICAgICAgICAgICAge3BrZy5zdWJ0aXRsZX1cclxuICAgICAgICAgICAgICA8L0JvZHlUZXh0PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1jb3Jtb3JhbnQgZm9udC1saWdodCB0ZXh0LWVudGVycHJpc2UtYnJvd24gbWItbWRcIj5cclxuICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShwa2cuaW52ZXN0bWVudC5hbW91bnQsIHBrZy5pbnZlc3RtZW50LmN1cnJlbmN5KX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8Q1RBQnV0dG9uIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgRG93aWVkeiBzacSZIHdpxJljZWpcclxuICAgICAgICAgICAgICA8L0NUQUJ1dHRvbj5cclxuICAgICAgICAgICAgPC9VbmlmaWVkQ2FyZD5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvc2VjdGlvbj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJTY3JvbGxSZXZlYWwiLCJTdGFnZ2VyQ29udGFpbmVyIiwiU3RhZ2dlckl0ZW0iLCJTZWN0aW9uVGl0bGUiLCJCb2R5VGV4dCIsIkxlYWRUZXh0IiwiQ2FyZFRpdGxlIiwiVW5pZmllZENhcmQiLCJDVEFCdXR0b24iLCJTZWNvbmRhcnlCdXR0b24iLCJpbnZlc3RtZW50UGFja2FnZXMiLCJnZXRSZXRyZWF0UGFja2FnZXMiLCJJY29uIiwiSW52ZXN0bWVudFNlY3Rpb24iLCJ0aXRsZSIsInN1YnRpdGxlIiwic2hvd09ubHlSZXRyZWF0cyIsInZhcmlhbnQiLCJzZWxlY3RlZFBhY2thZ2UiLCJzZXRTZWxlY3RlZFBhY2thZ2UiLCJwYWNrYWdlcyIsImxlbmd0aCIsImNvbnNvbGUiLCJ3YXJuIiwiZm9ybWF0UHJpY2UiLCJhbW91bnQiLCJjdXJyZW5jeSIsInBlcmlvZCIsImZvcm1hdHRlZEFtb3VudCIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJmb3JtYXQiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3R5bGUiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJiYWNrZ3JvdW5kU2l6ZSIsIm1hcCIsInBrZyIsImluZGV4IiwiaWQiLCJuYW1lIiwicGFkZGluZyIsIm9uQ2xpY2siLCJsZXZlbCIsInNpemUiLCJkdXJhdGlvbiIsImxvY2F0aW9uIiwiaW52ZXN0bWVudCIsImluc3RhbGxtZW50cyIsImF2YWlsYWJsZSIsInRlc3RpbW9uaWFsIiwidGV4dCIsImF1dGhvciIsImg0IiwiaGlnaGxpZ2h0cyIsImhpZ2hsaWdodCIsImkiLCJpbmNsdWRlZCIsImNhdGVnb3J5IiwiaDUiLCJ1bCIsIml0ZW1zIiwiaXRlbSIsImoiLCJsaSIsIm5vdEluY2x1ZGVkIiwib3B0aW9ucyIsIm9wdGlvbiIsInNwYW4iLCJkZXNjcmlwdGlvbiIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJDb21wYWN0SW52ZXN0bWVudCIsIm1heFBhY2thZ2VzIiwic2xpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvestmentSection.jsx\n"));

/***/ })

});
'use client';

import React, { useState } from 'react';
import { ScrollReveal, StaggerContainer, StaggerItem } from '@/components/ScrollReveal';
import { SectionTitle, BodyText, LeadText, CardTitle } from '@/components/ui/UnifiedTypography';
import UnifiedCard from '@/components/ui/UnifiedCard';
import { CTAButton, SecondaryButton } from '@/components/ui/UnifiedButton';
import { investmentPackages, getRetreatPackages } from '@/data/investmentData';
import { Icon } from '@/components/ui/IconSystem';

/**
 * InvestmentSection - Sekcja "Inwestycja w siebie"
 * <PERSON><PERSON><PERSON><PERSON> war<PERSON>, nie cenę - luksusowe podejście do prezentacji pakietów
 */

export default function InvestmentSection({ 
  title = "Inwestycja w Twoją Transformację",
  subtitle = "Każdy pakiet to starannie przygotowane doświadczenie, które zmieni Twoje życie na zawsze",
  showOnlyRetreats = false,
  variant = 'full' // 'full', 'compact', 'featured'
}) {
  const [selectedPackage, setSelectedPackage] = useState(null);
  const packages = showOnlyRetreats ? getRetreatPackages() : investmentPackages;

  // Safety check for packages data
  if (!packages || packages.length === 0) {
    console.warn('InvestmentSection: No packages data available');
    return null;
  }

  const formatPrice = (amount, currency, period) => {
    const formattedAmount = new Intl.NumberFormat('pl-PL').format(amount);
    return period ? `${formattedAmount} ${currency}/${period}` : `${formattedAmount} ${currency}`;
  };

  return (
    <section className="py-section bg-sanctuary relative overflow-hidden">
      {/* Luksusowy wzór w tle */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C19B68' fill-opacity='0.1'%3E%3Cpath d='M50 50c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '100px 100px'
          }}
        />
      </div>

      <div className="container mx-auto px-container-sm relative">
        <ScrollReveal variant="fadeInUp" className="text-center mb-2xl">
          <SectionTitle className="mb-md">
            {title}
          </SectionTitle>
          <LeadText className="text-sage max-w-3xl mx-auto">
            {subtitle}
          </LeadText>
        </ScrollReveal>

        <StaggerContainer className="grid grid-cols-1 lg:grid-cols-2 gap-xl max-w-6xl mx-auto">
          {packages.map((pkg, index) => {
            // Safety check for package structure
            if (!pkg || !pkg.id || !pkg.name) {
              console.warn('InvestmentSection: Invalid package data', pkg);
              return null;
            }

            return (
            <StaggerItem key={pkg.id}>
              <UnifiedCard 
                variant="elevated" 
                padding="xl"
                className="h-full group relative overflow-hidden cursor-pointer"
                onClick={() => setSelectedPackage(selectedPackage === pkg.id ? null : pkg.id)}
              >
                {/* Luksusowy akcent */}
                <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-enterprise-brown via-terra to-sand" />
                
                {/* Header pakietu */}
                <div className="mb-lg">
                  <div className="flex items-start justify-between mb-md">
                    <div className="flex-1">
                      <CardTitle level={3} className="mb-2 group-hover:text-enterprise-brown transition-colors duration-300">
                        {pkg.name}
                      </CardTitle>
                      <BodyText className="text-sage mb-sm">
                        {pkg.subtitle}
                      </BodyText>
                      <div className="flex items-center text-sm text-ash">
                        <Icon name="clock" size="xs" className="mr-2" />
                        {pkg.duration}
                        <Icon name="location" size="xs" className="ml-4 mr-2" />
                        {pkg.location}
                      </div>
                    </div>
                    
                    {/* Cena z luksusowym stylem */}
                    <div className="text-right ml-6">
                      <div className="text-3xl font-cormorant font-light text-charcoal mb-1">
                        {formatPrice(pkg.investment.amount, pkg.investment.currency, pkg.investment.period)}
                      </div>
                      {pkg.investment.installments?.available && (
                        <div className="text-xs text-sage">
                          lub w ratach
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Testimonial */}
                  {pkg.testimonial && (
                    <div className="bg-whisper p-4 rounded-lg border-l-4 border-enterprise-brown/20">
                      <BodyText className="text-sm italic text-charcoal-light mb-2">
                        "{pkg.testimonial.text}"
                      </BodyText>
                      <div className="text-xs text-sage font-medium">
                        — {pkg.testimonial.author}
                      </div>
                    </div>
                  )}
                </div>

                {/* Highlights */}
                <div className="mb-lg">
                  <h4 className="font-inter font-medium text-charcoal mb-sm text-sm uppercase tracking-wide">
                    Dlaczego to wyjątkowe
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {pkg.highlights.map((highlight, i) => (
                      <div key={i} className="flex items-center text-sm text-charcoal-light">
                        <Icon name="check" size="xs" className="text-enterprise-brown mr-2 flex-shrink-0" />
                        {highlight}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Rozwijane szczegóły */}
                <div className={`transition-all duration-500 overflow-hidden ${
                  selectedPackage === pkg.id ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="border-t border-stone-light/30 pt-lg mb-lg">
                    <h4 className="font-inter font-medium text-charcoal mb-md text-sm uppercase tracking-wide">
                      Co jest wliczone w inwestycję
                    </h4>
                    
                    <div className="space-y-md">
                      {pkg.included.map((category, i) => (
                        <div key={i}>
                          <h5 className="font-cormorant text-lg text-enterprise-brown mb-sm">
                            {category.category}
                          </h5>
                          <ul className="space-y-1">
                            {category.items.map((item, j) => (
                              <li key={j} className="flex items-start text-sm text-charcoal-light">
                                <Icon name="dot" size="xs" className="text-terra mr-2 mt-1.5 flex-shrink-0" />
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>

                    {/* Nie wliczone */}
                    {pkg.notIncluded && (
                      <div className="mt-lg pt-md border-t border-stone-light/20">
                        <h5 className="font-inter font-medium text-sage mb-sm text-sm">
                          Dodatkowo płatne:
                        </h5>
                        <ul className="space-y-1">
                          {pkg.notIncluded.map((item, i) => (
                            <li key={i} className="flex items-start text-sm text-sage">
                              <Icon name="minus" size="xs" className="mr-2 mt-1.5 flex-shrink-0" />
                              {item}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Opcje płatności */}
                    {pkg.investment.installments?.available && (
                      <div className="mt-lg p-md bg-linen rounded-lg">
                        <h5 className="font-inter font-medium text-charcoal mb-sm text-sm">
                          Opcje płatności:
                        </h5>
                        <div className="space-y-2">
                          {pkg.investment.installments.options.map((option, i) => (
                            <div key={i} className="flex items-center justify-between text-sm">
                              <span className="text-charcoal-light">{option.description}</span>
                              <span className="font-medium text-enterprise-brown">
                                {formatPrice(option.amount, pkg.investment.currency)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Przyciski akcji */}
                <div className="flex flex-col sm:flex-row gap-4 mt-lg">
                  <CTAButton 
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Logika rezerwacji
                    }}
                  >
                    <Icon name="heart" size="sm" className="mr-2" />
                    Zarezerwuj miejsce
                  </CTAButton>
                  <SecondaryButton 
                    className="sm:w-auto"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedPackage(selectedPackage === pkg.id ? null : pkg.id);
                    }}
                  >
                    {selectedPackage === pkg.id ? 'Zwiń szczegóły' : 'Zobacz szczegóły'}
                    <Icon 
                      name={selectedPackage === pkg.id ? "chevron-up" : "chevron-down"} 
                      size="sm" 
                      className="ml-2" 
                    />
                  </SecondaryButton>
                </div>
              </UnifiedCard>
            </StaggerItem>
            );
          })}
        </StaggerContainer>

        {/* Dodatkowe informacje */}
        <ScrollReveal variant="fadeInUp" className="text-center mt-2xl">
          <div className="max-w-3xl mx-auto">
            <BodyText className="text-sage mb-lg">
              Każdy pakiet został starannie przygotowany, aby zapewnić Ci pełne doświadczenie transformacji. 
              Inwestujesz nie tylko w podróż, ale w nową wersję siebie.
            </BodyText>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="flex items-center text-sm text-charcoal-light">
                <Icon name="shield" size="sm" className="text-enterprise-brown mr-2" />
                Gwarancja satysfakcji
              </div>
              <div className="flex items-center text-sm text-charcoal-light">
                <Icon name="users" size="sm" className="text-enterprise-brown mr-2" />
                Małe, kameralne grupy
              </div>
              <div className="flex items-center text-sm text-charcoal-light">
                <Icon name="star" size="sm" className="text-enterprise-brown mr-2" />
                7 lat doświadczenia
              </div>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
}

// Wariant kompaktowy dla innych stron
export function CompactInvestment({ maxPackages = 2 }) {
  const packages = getRetreatPackages().slice(0, maxPackages);
  
  return (
    <section className="py-xl bg-linen">
      <div className="container mx-auto px-container-sm">
        <ScrollReveal variant="fadeInUp" className="text-center mb-xl">
          <SectionTitle className="mb-md">
            Inwestuj w swoją transformację
          </SectionTitle>
        </ScrollReveal>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-lg max-w-4xl mx-auto">
          {packages.map((pkg) => (
            <UnifiedCard key={pkg.id} variant="warm" padding="lg" className="text-center">
              <CardTitle level={4} className="mb-sm">
                {pkg.name}
              </CardTitle>
              <BodyText className="text-sage mb-md">
                {pkg.subtitle}
              </BodyText>
              <div className="text-2xl font-cormorant font-light text-enterprise-brown mb-md">
                {formatPrice(pkg.investment.amount, pkg.investment.currency)}
              </div>
              <CTAButton size="sm" className="w-full">
                Dowiedz się więcej
              </CTAButton>
            </UnifiedCard>
          ))}
        </div>
      </div>
    </section>
  );
}
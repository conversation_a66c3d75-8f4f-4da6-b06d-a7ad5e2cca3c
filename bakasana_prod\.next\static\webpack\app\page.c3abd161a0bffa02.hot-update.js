"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_MinimalistHero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MinimalistHero */ \"(app-pages-browser)/./src/components/MinimalistHero.jsx\");\n/* harmony import */ var _components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/UnifiedTypography */ \"(app-pages-browser)/./src/components/ui/UnifiedTypography.jsx\");\n/* harmony import */ var _components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/UnifiedCard */ \"(app-pages-browser)/./src/components/ui/UnifiedCard.jsx\");\n/* harmony import */ var _components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/UnifiedButton */ \"(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\");\n/* harmony import */ var _components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/IconSystem */ \"(app-pages-browser)/./src/components/ui/IconSystem.jsx\");\n/* harmony import */ var _components_LuxuryTestimonials__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/LuxuryTestimonials */ \"(app-pages-browser)/./src/components/LuxuryTestimonials.jsx\");\n/* harmony import */ var _components_InvestmentSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/InvestmentSection */ \"(app-pages-browser)/./src/components/InvestmentSection.jsx\");\n/* harmony import */ var _components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/LuxuryElements */ \"(app-pages-browser)/./src/components/LuxuryElements.jsx\");\n/* harmony import */ var _components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ScrollReveal */ \"(app-pages-browser)/./src/components/ScrollReveal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// import { CompactWhatsApp } from '@/components/LuxuryWhatsApp';\n// import { MinimalScrollProgress } from '@/components/LuxuryScrollProgress';\n// Structured data for homepage SEO\nconst homepageStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"BAKASANA\",\n    \"alternateName\": \"Retreaty Jogi Bali Sri Lanka\",\n    \"url\": \"https://bakasana-travel.blog\",\n    \"logo\": \"https://bakasana-travel.blog/images/logo/bakasana-logo.png\",\n    \"description\": \"Najlepsze retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz\",\n    \"foundingDate\": \"2020\",\n    \"founder\": {\n        \"@type\": \"Person\",\n        \"name\": \"Julia Jakubowicz\",\n        \"jobTitle\": \"Instruktorka Jogi i Fizjoterapeutka\"\n    },\n    \"aggregateRating\": {\n        \"@type\": \"AggregateRating\",\n        \"ratingValue\": \"4.9\",\n        \"reviewCount\": \"150\",\n        \"bestRating\": \"5\"\n    },\n    \"priceRange\": \"2900-4200 PLN\"\n};\nconst testimonialsStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"itemListElement\": [\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Anna K.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Retreat na Bali z Julią był przełomowym momentem w moim życiu. Profesjonalne podejście połączone z ciepłem i autentycznością.\"\n        },\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Marta W.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Małe grupy, indywidualne podejście i magiczne miejsca. Sri Lanka z BAKASANA to doświadczenie, które zmienia perspektywę.\"\n        },\n        {\n            \"@type\": \"Review\",\n            \"author\": {\n                \"@type\": \"Person\",\n                \"name\": \"Karolina M.\"\n            },\n            \"reviewRating\": {\n                \"@type\": \"Rating\",\n                \"ratingValue\": \"5\",\n                \"bestRating\": \"5\"\n            },\n            \"reviewBody\": \"Julia łączy wiedzę fizjoterapeuty z duchowością jogi. Zajęcia online przygotowały mnie idealnie na retreat.\"\n        }\n    ]\n};\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(homepageStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(testimonialsStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MinimalistHero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-linen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-container-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                children: \"Odkryj magię jogi w duchowej Azji\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.LeadText, {\n                                children: \"Jestem Julia - certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka z 7-letnim doświadczeniem. Tworzę autentyczne retreaty na Bali i Sri Lanka, kt\\xf3re łączą głęboką praktykę jogi z odkrywaniem duchowego dziedzictwa Azji w małych, kameralnych grupach.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-sanctuary relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.02]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C19B68' fill-opacity='0.1'%3E%3Cpath d='M40 40c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                backgroundSize: '80px 80px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-container-sm relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.ScrollReveal, {\n                                variant: \"fadeInUp\",\n                                className: \"text-center mb-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                    children: \"Nasze Usługi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerContainer, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"lotus\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Retreaty na Bali\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"7-dniowe transformacyjne podr\\xf3że w Ubud. Praktyka jogi wśr\\xf3d taras\\xf3w ryżowych, medytacja w świątyniach, ayurveda i autentyczne poznawanie balińskiej kultury.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/retreaty\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Poznaj program\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"mountain\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Retreaty na Sri Lanka\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"10-dniowa duchowa podr\\xf3ż przez perłę Oceanu Indyjskiego. Joga na plażach, medytacja w g\\xf3rach, ayurveda i odkrywanie buddyjskich tradycji.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/retreaty\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Poznaj program\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"home\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Zajęcia Stacjonarne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Zajęcia jogi i fitness w przytulnym studio w Warszawie. Małe grupy, profesjonalna opieka i zapisy online przez system Fitssey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-stacjonarne\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Zapisz się na zajęcia\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"monitor\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Zajęcia Online\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Regularne sesje jogi online w małych grupach. Indywidualne podejście, korekty postawy i przygotowanie do retreat\\xf3w w Azji.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-online\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Dołącz do grupy\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollReveal__WEBPACK_IMPORTED_MODULE_11__.StaggerItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryCard, {\n                                            className: \"h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedCard__WEBPACK_IMPORTED_MODULE_5__.ServiceCard, {\n                                                className: \"text-center group h-full border-0 bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto mb-md bg-gradient-to-br from-enterprise-brown/10 to-terra/10 rounded-full flex items-center justify-center group-hover:from-enterprise-brown/20 group-hover:to-terra/20 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                    name: \"user\",\n                                                                    size: \"lg\",\n                                                                    className: \"text-enterprise-brown group-hover:text-terra transition-colors duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: \"Konsultacje Indywidualne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                                        className: \"text-sage mb-md\",\n                                                        children: \"Sesje terapeutyczne i konsultacje dostosowane do Twoich indywidualnych potrzeb. Zapisy przez system Fitssey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxuryButton, {\n                                                        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                        href: \"/zajecia-stacjonarne#konsultacje\",\n                                                        className: \"inline-flex items-center text-enterprise-brown hover:text-terra transition-all duration-300 font-light tracking-wide bg-transparent border-0 p-0\",\n                                                        goldAccent: false,\n                                                        children: [\n                                                            \"Um\\xf3w konsultację\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_IconSystem__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                name: \"arrow-right\",\n                                                                size: \"sm\",\n                                                                className: \"ml-2 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryTestimonials__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LuxuryElements__WEBPACK_IMPORTED_MODULE_10__.LuxurySeparator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                showOnlyRetreats: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-section bg-linen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-container-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.SectionTitle, {\n                                    children: \"Najczęściej zadawane pytania\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UnifiedTypography__WEBPACK_IMPORTED_MODULE_4__.BodyText, {\n                                    className: \"text-sage max-w-2xl mx-auto\",\n                                    children: \"Odpowiedzi na najważniejsze pytania dotyczące retreat\\xf3w jogi na Bali i Sri Lanka\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-md\",\n                            itemScope: true,\n                            itemType: \"https://schema.org/FAQPage\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm /* TODO: Replace with CardTitle */\",\n                                            itemProp: \"name\",\n                                            children: \"Czy retreaty są odpowiednie dla początkujących?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Tak! Nasze retreaty są dostosowane do wszystkich poziom\\xf3w zaawansowania. Julia jako doświadczona instruktorka zapewnia modyfikacje ćwiczeń i indywidualne podejście do każdego uczestnika.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm\",\n                                            itemProp: \"name\",\n                                            children: \"Ile os\\xf3b uczestniczy w retreatach?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Nasze grupy są małe i kameralne - maksymalnie 10-12 os\\xf3b. Dzięki temu każdy uczestnik otrzymuje indywidualną uwagę i może w pełni doświadczyć transformacyjnej mocy jogi.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-sanctuary p-6 shadow-subtle\",\n                                    itemScope: true,\n                                    itemType: \"https://schema.org/Question\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-cormorant text-charcoal mb-sm\",\n                                            itemProp: \"name\",\n                                            children: \"Co jest wliczone w cenę retreatu?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            itemScope: true,\n                                            itemType: \"https://schema.org/Answer\",\n                                            itemProp: \"acceptedAnswer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-charcoal-light leading-relaxed\",\n                                                itemProp: \"text\",\n                                                children: \"Cena obejmuje zakwaterowanie, wszystkie posiłki, codzienne sesje jogi, medytację, wycieczki kulturalne, transport lokalny oraz opiekę doświadczonej instruktorki przez cały pobyt.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.jsx\n"));

/***/ })

});